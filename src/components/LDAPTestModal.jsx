import React, { useState } from 'react';
import { FaNetworkWired, FaTimes, FaCheck, FaExclamationTriangle } from 'react-icons/fa';

const LDAPTestModal = ({ isOpen, onClose }) => {
  const [testResults, setTestResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const runConnectionTest = async () => {
    setIsLoading(true);
    setTestResults(null);

    try {
      const response = await fetch('/api/ldap/test');
      const result = await response.json();
      
      setTestResults({
        success: response.ok && result.success,
        message: result.message || result.error,
        server: result.server,
        statusCode: response.status
      });
    } catch (error) {
      setTestResults({
        success: false,
        message: '无法连接到LDAP认证服务',
        error: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runEnvironmentTest = async () => {
    setIsLoading(true);
    setTestResults(null);

    try {
      const response = await fetch('/api/ldap/environments');
      const result = await response.json();
      
      setTestResults({
        success: response.ok && result.success,
        message: result.success ? 
          `成功获取到 ${result.environments?.length || 0} 个LDAP环境` : 
          result.error,
        environments: result.environments,
        statusCode: response.status
      });
    } catch (error) {
      setTestResults({
        success: false,
        message: '无法获取LDAP环境列表',
        error: error.message
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg p-6 w-full max-w-md mx-4 border border-gray-700">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white flex items-center gap-2">
            <FaNetworkWired className="text-cyan-400" />
            LDAP连接测试
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FaTimes size={20} />
          </button>
        </div>

        <div className="space-y-4">
          <button
            onClick={runConnectionTest}
            disabled={isLoading}
            className="w-full py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? '测试中...' : '测试LDAP服务器连接'}
          </button>

          <button
            onClick={runEnvironmentTest}
            disabled={isLoading}
            className="w-full py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? '测试中...' : '测试环境配置'}
          </button>

          {testResults && (
            <div className={`p-4 rounded-lg border ${
              testResults.success 
                ? 'bg-green-900 border-green-600 text-green-100' 
                : 'bg-red-900 border-red-600 text-red-100'
            }`}>
              <div className="flex items-center gap-2 mb-2">
                {testResults.success ? (
                  <FaCheck className="text-green-400" />
                ) : (
                  <FaExclamationTriangle className="text-red-400" />
                )}
                <span className="font-semibold">
                  {testResults.success ? '测试成功' : '测试失败'}
                </span>
              </div>
              
              <p className="text-sm mb-2">{testResults.message}</p>
              
              {testResults.server && (
                <p className="text-xs opacity-75">服务器: {testResults.server}</p>
              )}
              
              {testResults.statusCode && (
                <p className="text-xs opacity-75">状态码: {testResults.statusCode}</p>
              )}

              {testResults.environments && (
                <div className="mt-3">
                  <p className="text-sm font-semibold mb-1">可用环境:</p>
                  <ul className="text-xs space-y-1">
                    {testResults.environments.map((env, index) => (
                      <li key={index} className="opacity-75">
                        • {env.name} ({env.id})
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="mt-6 text-xs text-gray-400">
          <p>此工具用于诊断LDAP连接问题。</p>
          <p>如果测试失败，请检查网络连接和LDAP服务器状态。</p>
        </div>
      </div>
    </div>
  );
};

export default LDAPTestModal;
