{"description": "硬编码白名单配置 - 允许某些文件包含硬编码值", "version": "2.0.0", "rules": {"urls": {"description": "允许包含硬编码URL的文件", "whitelist": ["***************", "localhost", "127.0.0.1"], "files": ["vite.config.js", "src/config/apiConfig.js", "scripts/*.cjs"]}, "ips": {"description": "允许包含硬编码IP地址的文件", "whitelist": ["***************"], "files": ["ecosystem.config.js"]}, "ports": {"description": "允许包含硬编码端口的文件", "whitelist": [3001, 3002, 4000, 5173], "files": ["vite.config.js", "src/config/apiConfig.js"]}}, "exceptions": {"description": "特殊例外配置", "development_configs": {"files": ["env.example", "vite.config.js"], "reason": "开发环境配置文件，需要包含示例值"}}}