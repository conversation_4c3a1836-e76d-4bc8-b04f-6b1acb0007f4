<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档数量测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #2a2a2a;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            padding: 10px;
            background-color: #333;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #00bcd4;
        }
        .stat-label {
            font-size: 12px;
            color: #999;
        }
        button {
            background-color: #00bcd4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0097a7;
        }
        .log {
            background-color: #000;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>文档数量一致性测试</h1>
    
    <div class="test-section">
        <h2>测试步骤</h2>
        <p>1. 点击"初始加载"按钮，观察文档数量</p>
        <p>2. 点击"重新加载全部文档"按钮，观察文档数量是否一致</p>
        <p>3. 检查控制台日志，确认加载过程</p>
        
        <button onclick="initialLoad()">初始加载</button>
        <button onclick="reloadAllDocs()">重新加载全部文档</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="test-section">
        <h2>文档统计</h2>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="totalDocs">-</div>
                <div class="stat-label">总文档数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="categories">-</div>
                <div class="stat-label">分类数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="loadTime">-</div>
                <div class="stat-label">加载时间(ms)</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>加载日志</h2>
        <div class="log" id="log"></div>
    </div>

    <script>
        // 模拟documentService
        class DocumentService {
            constructor() {
                this.documentCache = new Map();
                this.allDocumentsLoaded = false;
                this.loading = false;
                this.preloadPromise = null;
            }

            async getDocumentConfig() {
                const response = await fetch('/docs/config.json');
                return await response.json();
            }

            async getCategories() {
                const config = await this.getDocumentConfig();
                return config.categories || [];
            }

            async preloadAllDocuments() {
                if (this.allDocumentsLoaded) {
                    log('文档已预加载，跳过');
                    return;
                }
                
                log('开始预加载所有文档...');
                const startTime = Date.now();
                
                await this.getAllDocumentsContent();
                
                const endTime = Date.now();
                log(`文档预加载完成，耗时: ${endTime - startTime}ms`);
                
                return this.getDocumentStats();
            }

            async getAllDocumentsContent() {
                if (this.allDocumentsLoaded && this.documentCache.size > 0) {
                    log('使用缓存的文档数据');
                    return this.organizeDocuments();
                }

                if (this.loading) {
                    log('等待正在进行的加载...');
                    return await this.preloadPromise;
                }

                log('开始加载文档...');
                this.loading = true;
                this.preloadPromise = this.loadAllDocuments();

                try {
                    const result = await this.preloadPromise;
                    this.allDocumentsLoaded = true;
                    this.loading = false;
                    log('文档加载完成');
                    return result;
                } catch (error) {
                    log('加载文档失败: ' + error.message);
                    this.loading = false;
                    return {};
                }
            }

            async loadAllDocuments() {
                const config = await this.getDocumentConfig();
                const loadPromises = [];

                // 并行加载所有文档
                for (const [categoryKey, documents] of Object.entries(config.documents)) {
                    for (const doc of documents) {
                        loadPromises.push(
                            this.loadDocument(doc.file).then(content => ({
                                categoryKey,
                                doc,
                                content
                            }))
                        );
                    }
                }

                // 等待所有文档加载完成
                const results = await Promise.all(loadPromises);
                
                log(`成功加载 ${results.length} 个文档`);
                log(`当前缓存中有 ${this.documentCache.size} 个文档`);
                
                return this.organizeDocuments();
            }

            async loadDocument(filePath) {
                const cacheKey = `doc_${filePath}`;
                
                if (this.documentCache.has(cacheKey)) {
                    return this.documentCache.get(cacheKey);
                }

                try {
                    log(`正在加载文档: ${filePath}`);
                    const response = await fetch(`/docs/${filePath}`);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const content = await response.text();

                    if (!content.trim()) {
                        throw new Error('文档内容为空');
                    }

                    const parsedContent = {
                        filePath,
                        rawContent: content,
                        wordCount: content.split(/\s+/).length,
                        lastParsed: new Date().toISOString()
                    };

                    this.documentCache.set(cacheKey, parsedContent);
                    log(`文档加载成功: ${filePath} (${parsedContent.wordCount} 词)`);
                    return parsedContent;
                } catch (error) {
                    log(`加载文档失败: ${filePath} - ${error.message}`);
                    return null;
                }
            }

            organizeDocuments() {
                const organized = {};
                
                for (const [cacheKey, content] of this.documentCache.entries()) {
                    if (!content) continue;
                    
                    const filePath = content.filePath;
                    const category = filePath.split('/')[0];
                    
                    if (!organized[category]) {
                        organized[category] = [];
                    }

                    const docMetadata = this.getDocumentMetadata(filePath);

                    organized[category].push({
                        ...docMetadata,
                        filePath,
                        content,
                        id: docMetadata?.id || filePath.replace(/[^a-zA-Z0-9]/g, '-'),
                        title: docMetadata?.title || filePath,
                        description: docMetadata?.description || '文档描述',
                        tags: docMetadata?.tags || [],
                        readTime: docMetadata?.readTime || '5 分钟',
                        file: docMetadata?.file || filePath,
                    });
                }

                return organized;
            }

            getDocumentMetadata(filePath) {
                // 这里应该从配置中获取，简化处理
                return {
                    id: filePath.replace(/[^a-zA-Z0-9]/g, '-'),
                    title: filePath,
                    description: '文档描述',
                    readTime: '5 分钟',
                    tags: [],
                    file: filePath
                };
            }

            async getDocumentsByCategory(category) {
                const allDocs = await this.getAllDocumentsContent();
                
                if (category === 'all') {
                    const flatDocuments = [];
                    for (const [categoryKey, docs] of Object.entries(allDocs)) {
                        docs.forEach(doc => {
                            flatDocuments.push({
                                ...doc,
                                category: categoryKey
                            });
                        });
                    }
                    return flatDocuments;
                }
                
                return allDocs[category] || [];
            }

            async getDocumentStats() {
                const documents = await this.getAllDocumentsContent();
                const stats = {
                    totalDocuments: 0,
                    categories: Object.keys(documents).length
                };

                for (const docs of Object.values(documents)) {
                    stats.totalDocuments += docs.length;
                }

                return stats;
            }
        }

        const documentService = new DocumentService();

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStats(totalDocs, categories, loadTime) {
            document.getElementById('totalDocs').textContent = totalDocs;
            document.getElementById('categories').textContent = categories;
            document.getElementById('loadTime').textContent = loadTime;
        }

        async function initialLoad() {
            log('=== 开始初始加载测试 ===');
            const startTime = Date.now();
            
            try {
                // 模拟初始加载逻辑
                const categoriesData = await documentService.getCategories();
                log(`加载了 ${categoriesData.length} 个分类`);
                
                // 确保所有文档都已加载完成
                await documentService.preloadAllDocuments();
                
                // 加载初始文档列表
                const docsData = await documentService.getDocumentsByCategory('all');
                
                const endTime = Date.now();
                const loadTime = endTime - startTime;
                
                log(`初始加载完成，获得 ${docsData.length} 个文档`);
                updateStats(docsData.length, categoriesData.length - 1, loadTime);
                
            } catch (error) {
                log('初始加载失败: ' + error.message);
            }
        }

        async function reloadAllDocs() {
            log('=== 开始重新加载全部文档测试 ===');
            const startTime = Date.now();
            
            try {
                const docsData = await documentService.getDocumentsByCategory('all');
                
                const endTime = Date.now();
                const loadTime = endTime - startTime;
                
                log(`重新加载完成，获得 ${docsData.length} 个文档`);
                updateStats(docsData.length, '-', loadTime);
                
            } catch (error) {
                log('重新加载失败: ' + error.message);
            }
        }

        // 页面加载完成后自动运行初始测试
        window.addEventListener('load', () => {
            log('页面加载完成，准备测试');
        });
    </script>
</body>
</html>
