# 生产环境LDAP登录问题排查指南

## 问题概述

在生产环境中，如果遇到登录功能无法正常工作的问题，通常是由于LDAP服务器连接问题导致的。本文档提供了完整的排查和解决方案。

## 快速诊断

### 1. 使用内置LDAP测试工具

在网页界面中，点击登录按钮旁边的 🔧 按钮，打开LDAP连接测试工具：

- **测试LDAP服务器连接**：检查基础网络连接
- **测试环境配置**：验证LDAP环境配置是否正确

### 2. 检查容器状态

```bash
# 查看容器运行状态
docker-compose ps

# 查看应用容器日志
docker logs ynnx-ai-platform --tail=50

# 查看nginx代理日志
docker logs ynnx-nginx --tail=20
```

## 常见问题及解决方案

### 问题1：LDAP服务器连接超时

**症状**：
- 浏览器控制台显示 `POST /api/ldap/authenticate 503 (Service Unavailable)`
- 应用日志显示 `connection timeout` 错误

**解决方案**：

1. **检查网络连通性**
```bash
# 进入应用容器测试网络连接
docker exec ynnx-ai-platform ping **************
docker exec ynnx-ai-platform telnet ************** 389
```

2. **检查防火墙设置**
确保生产服务器能够访问LDAP服务器的389端口：
```bash
# 在宿主机上测试
telnet ************** 389
telnet 132.147.255.2 389
```

3. **调整超时设置**
如果网络延迟较高，可以增加超时时间：
```yaml
# 在docker-compose.yml中调整
environment:
  - LDAP_CONNECTION_TIMEOUT=30000  # 增加到30秒
  - LDAP_SEARCH_TIMEOUT=20000      # 增加到20秒
```

### 问题2：LDAP认证失败

**症状**：
- 浏览器控制台显示 `POST /api/ldap/authenticate 401 (Unauthorized)`
- 用户名密码正确但无法登录

**解决方案**：

1. **验证LDAP配置**
检查 `docker-compose.yml` 中的LDAP环境配置：
```yaml
# DEVVDI环境配置示例
- LDAP_DEVVDI_ENV_URL=ldap://**************:389
- LDAP_DEVVDI_ENV_BASE_DN=DC=DEVVDI,DC=YNRCC,DC=COM
- LDAP_DEVVDI_ENV_USER_FILTER=(userPrincipalName={{username}}@DEVVDI.YNRCC.COM)
- LDAP_DEVVDI_ENV_USER_DN_PATTERN={{username}}@DEVVDI.YNRCC.COM
```

2. **测试用户DN格式**
使用调试API测试用户查找：
```bash
curl "https://your-domain.com/api/ldap/find-user/your-username"
```

### 问题3：nginx代理配置问题

**症状**：
- 请求无法到达后端服务
- nginx访问日志中没有相关请求记录

**解决方案**：

1. **检查nginx配置**
确认 `/api/ldap/` 路径的代理配置正确：
```nginx
location /api/ldap/ {
    proxy_pass http://ldap_backend/api/ldap/;
    proxy_http_version 1.1;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_connect_timeout 5s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
}
```

2. **检查上游服务器配置**
```nginx
upstream ldap_backend {
    server app:3002;
    keepalive 8;
}
```

## 生产环境部署检查清单

### 部署前检查

- [ ] 确认LDAP服务器地址和端口正确
- [ ] 验证网络连通性（ping和telnet测试）
- [ ] 检查防火墙规则
- [ ] 确认LDAP用户DN格式和过滤器配置

### 部署后验证

- [ ] 使用内置LDAP测试工具验证连接
- [ ] 测试已知用户账号登录
- [ ] 检查应用和nginx日志
- [ ] 验证错误处理和用户提示

### 监控和维护

- [ ] 设置LDAP连接监控
- [ ] 配置日志轮转
- [ ] 定期检查LDAP服务器状态
- [ ] 备份配置文件

## 错误代码说明

| HTTP状态码 | 含义 | 解决方案 |
|-----------|------|----------|
| 401 | 用户名或密码错误 | 检查用户凭据和LDAP配置 |
| 503 | LDAP服务器不可用 | 检查网络连接和服务器状态 |
| 500 | 内部服务器错误 | 检查应用日志和配置 |

## 联系支持

如果按照本指南仍无法解决问题，请提供以下信息：

1. 错误截图和浏览器控制台日志
2. 应用容器日志（最近50行）
3. nginx访问日志
4. LDAP测试工具的测试结果
5. 网络连通性测试结果

## 更新日志

- 2025-07-21: 添加了改进的错误处理和LDAP测试工具
- 2025-07-21: 优化了错误信息显示，区分网络问题和认证问题
