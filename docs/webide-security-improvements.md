# Web IDE 安全性优化

## 优化目标
1. 移除"在新窗口中打开"功能，防止用户脱离平台独立使用OpenVSCode
2. 确保用户不易获取到真实OpenVSCode地址
3. 增强iframe安全性和访问控制

## 实施的安全措施

### 1. 移除新窗口打开功能

#### 前端代码修改
- **移除按钮**: 删除了"在新窗口中打开"按钮及其相关UI
- **移除快捷键**: 删除了 `Ctrl+Shift+I` 快捷键支持
- **移除函数**: 删除了 `openInNewWindow()` 函数

```javascript
// 移除前
if (e.ctrlKey && e.shiftKey && e.key === 'I' && user) {
  e.preventDefault();
  openInNewWindow();
}

// 移除后 - 仅保留全屏切换
if (e.key === 'F11' && user) {
  e.preventDefault();
  setIsFullscreen(!isFullscreen);
}
```

### 2. 地址隐藏和安全代理

#### nginx代理配置
- **相对路径**: 使用 `/ide/` 路径代理到真实OpenVSCode服务
- **地址重写**: nginx自动处理URL重写，隐藏真实地址
- **安全头部**: 添加安全相关的HTTP头部

```nginx
location /ide/ {
    rewrite ^/ide/(.*)$ /$1 break;
    proxy_pass http://***************:3667;
    
    # 安全头部设置
    proxy_hide_header Server;
    proxy_hide_header X-Powered-By;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

#### 环境变量清理
- **移除暴露**: 从Dockerfile和docker-compose.yml中移除OpenVSCode相关环境变量
- **代码中使用**: 代码中直接使用相对路径 `/ide/`，不依赖环境变量

### 3. iframe安全增强

#### 沙箱限制
```javascript
// 限制iframe权限，仅允许必要功能
sandbox="allow-same-origin allow-scripts allow-forms allow-modals allow-pointer-lock"

// 权限策略限制
allow="clipboard-read; clipboard-write"

// 安全引用策略
referrerPolicy="no-referrer"
```

#### 安全属性
- **loading="lazy"**: 延迟加载
- **importance="high"**: 高优先级
- **移除危险权限**: 移除了 `allow-popups`、`allow-downloads`、`allow-top-navigation-by-user-activation` 等权限

### 4. 前端安全措施

#### 开发者工具限制
```javascript
// 防止通过控制台获取真实地址
const preventDevTools = (e) => {
  if (e.key === 'F12' || 
      (e.ctrlKey && e.shiftKey && e.key === 'I') ||
      (e.ctrlKey && e.key === 'u')) {
    e.preventDefault();
    e.stopPropagation();
    return false;
  }
};

// 禁用右键菜单
const preventContextMenu = (e) => {
  e.preventDefault();
  return false;
};
```

### 5. 用户界面优化

#### 安全提示
- **状态显示**: 显示"安全内嵌环境"提示
- **移除误导**: 移除了可能暴露外部访问的UI元素
- **保留功能**: 保留全屏功能，提供更好的使用体验

## 安全效果

### 地址隐藏效果
1. **用户视角**: 用户只能看到 `/ide/` 路径
2. **开发者工具**: 即使查看网络请求，也只能看到相对路径
3. **源码检查**: 前端代码中不包含真实OpenVSCode地址

### 访问控制效果
1. **iframe限制**: 只能在本平台内使用，无法独立访问
2. **功能限制**: 移除了可能导致用户脱离平台的功能
3. **权限最小化**: iframe权限被限制到最小必要集合

### 用户体验保持
1. **功能完整**: OpenVSCode的核心功能完全保留
2. **性能优化**: 自适应高度提供更好的显示效果
3. **操作便利**: 保留全屏等必要的用户操作

## 部署验证

### 构建测试
```bash
# 清理并重新构建
docker-compose down -v
npm run build
docker-compose build --no-cache
docker-compose up -d
```

### 安全检查
1. **地址检查**: 确认前端代码中无真实OpenVSCode地址
2. **功能测试**: 确认移除的功能确实不可用
3. **iframe测试**: 确认OpenVSCode只能在平台内使用

## 注意事项

### 维护要点
1. **nginx配置**: 确保nginx代理配置正确
2. **环境变量**: 避免在新的配置中重新引入OpenVSCode地址
3. **代码审查**: 新增代码时注意不要暴露真实地址

### 兼容性
- 所有现有功能保持兼容
- 用户体验无负面影响
- 安全性显著提升

## 总结

通过以上安全优化措施，成功实现了：
- ✅ 移除新窗口打开功能
- ✅ 隐藏真实OpenVSCode地址
- ✅ 增强iframe安全性
- ✅ 保持用户体验
- ✅ 提升整体安全性

用户现在只能在平台内使用Web IDE，无法轻易获取真实OpenVSCode地址或独立访问服务。
