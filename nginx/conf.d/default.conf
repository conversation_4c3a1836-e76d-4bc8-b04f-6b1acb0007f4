# 上游服务器配置
upstream ldap_backend {
    server app:3002;
    keepalive 8;
}

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# HTTP服务器 - 重定向到HTTPS，但允许内部请求
server {
    listen 80;
    server_name localhost ***************;

    # 允许Docker内部网络的请求（OpenVSCode内部通信）
    location /error {
        return 404;
    }

    # 其他请求重定向到HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# WebSocket专用服务器 - HTTP/1.1 only
server {
    listen 8443 ssl;
    # 不启用HTTP/2，强制使用HTTP/1.1
    server_name localhost ***************;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 专门处理WebSocket连接
    location ~ ^/insider-[a-f0-9]+$ {
        access_log /var/log/nginx/websocket.log main;

        proxy_pass http://***************:3667;
        proxy_http_version 1.1;
        proxy_set_header Host ***************:3667;
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # WebSocket支持
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 3600s;
        proxy_read_timeout 3600s;

        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;
    }
}

# HTTPS服务器 - 对WebSocket请求禁用HTTP/2
server {
    listen 443 ssl;
    # 条件性启用HTTP/2：对WebSocket请求使用HTTP/1.1
    http2 on;
    server_name localhost ***************;
    root /usr/share/nginx/html;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 增加客户端请求体大小限制
    client_max_body_size 10M;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # LiteLLM API代理
    location /api/litellm/ {
        proxy_pass http://***************:4000/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # CORS头部支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }

    # LDAP认证API代理
    location /api/ldap/ {
        proxy_pass http://ldap_backend/api/ldap/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 5s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # OpenVSCode CSS文件代理 - 修复MIME类型
    location ~* ^/(insider-[a-f0-9]+|static|out|extensions|node_modules)/.*\.css$ {
        proxy_pass http://***************:3667;
        proxy_http_version 1.1;
        proxy_set_header Host ***************:3667;
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # 强制设置正确的MIME类型和CORS头
        proxy_hide_header Content-Type;
        add_header Content-Type "text/css" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
        add_header Cache-Control "public, max-age=31536000";
        expires 1y;
        access_log off;
    }

    # OpenVSCode JS文件代理 - 修复MIME类型
    location ~* ^/(insider-[a-f0-9]+|static|out|extensions|node_modules)/.*\.(js|mjs)$ {
        proxy_pass http://***************:3667;
        proxy_http_version 1.1;
        proxy_set_header Host ***************:3667;
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # 强制设置正确的MIME类型和CORS头
        proxy_hide_header Content-Type;
        add_header Content-Type "application/javascript" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
        add_header Cache-Control "public, max-age=31536000";
        expires 1y;
        access_log off;
    }

    # OpenVSCode JSON文件代理 - 修复MIME类型
    location ~* ^/(insider-[a-f0-9]+|static|out|extensions|node_modules)/.*\.json$ {
        proxy_pass http://***************:3667;
        proxy_http_version 1.1;
        proxy_set_header Host ***************:3667;
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # 强制设置正确的MIME类型
        proxy_hide_header Content-Type;
        add_header Content-Type "application/json" always;
        add_header Cache-Control "public, max-age=31536000";
        expires 1y;
        access_log off;
    }

    # OpenVSCode WASM文件代理 - 修复MIME类型
    location ~* ^/(insider-[a-f0-9]+|static|out|extensions|node_modules)/.*\.wasm$ {
        proxy_pass http://***************:3667;
        proxy_http_version 1.1;
        proxy_set_header Host ***************:3667;
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # 强制设置正确的MIME类型
        proxy_hide_header Content-Type;
        add_header Content-Type "application/wasm" always;
        add_header Cache-Control "public, max-age=31536000";
        expires 1y;
        access_log off;
    }

    # OpenVSCode WebSocket连接代理 - 使用前缀匹配确保最高优先级
    location ^~ /insider- {
        # 处理缺失的VSDA文件 - 返回空响应避免404错误
        if ($uri ~* /vsda_bg\.wasm$) {
            add_header Content-Type "application/wasm";
            add_header Access-Control-Allow-Origin "*";
            return 204;
        }

        if ($uri ~* /vsda\.js$) {
            add_header Content-Type "application/javascript";
            add_header Access-Control-Allow-Origin "*";
            return 200 '// VSDA module stub - disabled in offline mode\nif (typeof define === "function") { define([], function() { return {}; }); }';
        }

        # 记录WebSocket升级请求
        access_log /var/log/nginx/websocket.log main;

        proxy_pass http://***************:3667;
        proxy_http_version 1.1;
        proxy_set_header Host ***************:3667;
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # WebSocket支持
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 添加CORS头支持iframe访问
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;

        # 超时设置 - WebSocket需要更长的超时时间
        proxy_connect_timeout 10s;
        proxy_send_timeout 3600s;
        proxy_read_timeout 3600s;

        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # 处理缺失的VSDA文件 - 直接返回空响应避免控制台错误
    location ~* /vsda_bg\.wasm$ {
        add_header Content-Type "application/wasm";
        return 204;
        access_log off;
    }

    location ~* /vsda\.js$ {
        add_header Content-Type "application/javascript";
        return 204;
        access_log off;
    }

    # 处理OpenVSCode的外部API请求 - 在内网环境下返回空响应
    location ~* /vscode/gallery/ {
        add_header Content-Type "application/json";
        add_header Access-Control-Allow-Origin "*";
        return 200 '{"extensions":[],"totalSize":0}';
        access_log off;
    }

    # 处理OpenVSCode对open-vsx.org的API请求
    location ~* ^/(api/)?vscode/gallery {
        add_header Content-Type "application/json";
        add_header Access-Control-Allow-Origin "*";
        return 200 '{"extensions":[],"totalSize":0}';
        access_log off;
    }

    # 提供VSCode配置文件
    location = /vscode-settings.json {
        alias /usr/share/nginx/html/vscode-settings.json;
        add_header Content-Type "application/json";
        add_header Access-Control-Allow-Origin "*";
        expires 1h;
    }

    # 拦截所有对外部扩展仓库的请求
    location ~* (open-vsx\.org|marketplace\.visualstudio\.com) {
        add_header Content-Type "application/json";
        add_header Access-Control-Allow-Origin "*";
        return 200 '{"extensions":[],"totalSize":0,"message":"Extension marketplace disabled in offline mode"}';
        access_log off;
    }

    # OpenVSCode其他静态资源代理
    location ~* ^/(insider-[a-f0-9]+|static|out|extensions|node_modules)/ {
        proxy_pass http://***************:3667;
        proxy_http_version 1.1;
        proxy_set_header Host ***************:3667;
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # 添加CORS头支持iframe访问
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;
        add_header Cache-Control "public, max-age=31536000";
        expires 1y;
        access_log off;
    }

    # OpenVSCode代理 - 完整的静态资源和WebSocket支持
    location /ide/ {
        # 移除前缀，直接代理到OpenVSCode
        rewrite ^/ide/(.*)$ /$1 break;
        proxy_pass http://***************:3667;
        proxy_http_version 1.1;

        # 关键：保持Host头和添加认证token
        proxy_set_header Host ***************:3667;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket支持 - 确保正确的升级头
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 解决Cookie问题：在代理层自动添加认证Cookie
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";

        # 超时设置 - WebSocket需要更长的超时时间
        proxy_connect_timeout 10s;
        proxy_send_timeout 3600s;
        proxy_read_timeout 3600s;

        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;

        # 安全头部设置 - 限制iframe只能在同源使用
        proxy_hide_header X-Frame-Options;
        add_header X-Frame-Options "SAMEORIGIN" always;

        # 隐藏服务器信息，防止暴露真实地址
        proxy_hide_header Server;
        proxy_hide_header X-Powered-By;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;

        # 防止直接访问，只允许通过本站iframe嵌入
        # 注释掉严格的referer检查，因为可能影响正常使用
        # if ($http_referer !~* "^https?://[^/]*$host") {
        #     return 403;
        # }
    }

    # LobeChat /webapi 路径代理 - 处理聊天API请求
    location ~* ^/webapi/ {
        # 记录请求用于调试
        access_log /var/log/nginx/lobechat_webapi.log;

        # 直接代理到LobeChat服务
        proxy_pass http://***************:3210;
        proxy_http_version 1.1;

        # 关键：保持Host头和添加认证信息
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # WebSocket支持 - 确保正确的升级头
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 为LobeChat添加认证信息
        proxy_set_header X-Auth-User $http_x_auth_user;
        proxy_set_header X-Auth-Email $http_x_auth_email;
        proxy_set_header X-Auth-Name $http_x_auth_name;
        proxy_set_header X-Platform-Auth "ynnx-platform";
        proxy_set_header X-Platform-Token "ynnx-chat-token-2025";

        # 超时设置 - API请求可能需要较长时间
        proxy_connect_timeout 10s;
        proxy_send_timeout 3600s;
        proxy_read_timeout 3600s;

        # 缓冲设置 - 对于流式响应很重要
        proxy_buffering off;
        proxy_request_buffering off;

        # 添加调试头
        add_header X-Proxy-Source "lobechat-webapi" always;
    }

    # LobeChat /icons 路径代理 - 专门处理图标页面和图标资源
    location ~* ^/icons(/.*)?$ {
        # 记录请求用于调试
        access_log /var/log/nginx/lobechat_icons.log;

        # 直接代理到LobeChat服务
        proxy_pass http://***************:3210;
        proxy_http_version 1.1;

        # 基本代理头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 静态资源缓存设置
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";

        # 允许跨域访问静态资源
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;

        # 添加调试头
        add_header X-Proxy-Source "lobechat-icons" always;
    }

    # LobeChat通用静态资源代理 - 捕获所有可能的静态资源请求
    # 这个规则会捕获所有以下模式的请求并代理到LobeChat：
    # - /_next/static/* (Next.js静态资源)
    # - /_next/image* (Next.js图片优化)
    # - /favicon*.* (各种favicon文件)
    # - /apple-touch-icon*.* (苹果图标)
    # - /manifest.* (PWA manifest文件)
    # - /robots.txt, /sitemap.xml (SEO文件)
    # - /*.ico, /*.png, /*.svg (根目录图标文件)
    location ~* ^/((_next/(static/.*|image))|favicon[^/]*\.(ico|png|svg)|apple-touch-icon[^/]*\.(png|svg)|manifest\.(json|webmanifest)|robots\.txt|sitemap\.xml|[^/]*\.(ico|png|svg))(\?.*)?$ {
        # 记录请求用于调试
        access_log /var/log/nginx/lobechat_static.log;

        # 直接代理到LobeChat服务
        proxy_pass http://***************:3210;
        proxy_http_version 1.1;

        # 基本代理头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 静态资源缓存设置
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";

        # 允许跨域访问静态资源
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;

        # 不添加nosniff头部，让浏览器正确识别MIME类型
        proxy_hide_header X-Content-Type-Options;

        # 添加调试头部
        add_header X-Proxy-Source "lobechat-static" always;
    }

    # LobeChat带前缀的 /icons 路径代理 - 处理 /chat/icons 路径
    location ~* ^/chat/icons(/.*)?$ {
        # 移除/chat前缀，直接代理到LobeChat
        rewrite ^/chat/(.*)$ /$1 break;
        proxy_pass http://***************:3210;
        proxy_http_version 1.1;

        # 基本代理头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 静态资源缓存设置
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";

        # 允许跨域访问静态资源
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;

        # 添加调试头部
        add_header X-Proxy-Source "lobechat-prefixed-icons" always;

        access_log /var/log/nginx/lobechat_prefixed_icons.log;
    }

    # LobeChat带前缀的静态资源代理 - 处理重写后的路径
    location ~* ^/chat/((_next/(static/.*|image))|favicon[^/]*\.(ico|png|svg)|apple-touch-icon[^/]*\.(png|svg)|manifest\.(json|webmanifest)|robots\.txt|sitemap\.xml|[^/]*\.(ico|png|svg))(\?.*)?$ {
        # 移除/chat前缀，直接代理到LobeChat
        rewrite ^/chat/(.*)$ /$1 break;
        proxy_pass http://***************:3210;
        proxy_http_version 1.1;

        # 基本代理头部
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 静态资源缓存设置
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";

        # 允许跨域访问静态资源
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;

        # 不添加nosniff头部，让浏览器正确识别MIME类型
        proxy_hide_header X-Content-Type-Options;

        # 添加调试头部
        add_header X-Proxy-Source "lobechat-prefixed" always;

        access_log /var/log/nginx/lobechat_prefixed.log;
    }

    # LobeChat 认证 API 模拟 - 解决认证配置问题
    location ~* ^/chat/api/auth/(session|providers|csrf)$ {
        # 为认证相关的 API 提供模拟响应，避免 500 错误
        add_header Content-Type "application/json" always;

        # 根据不同的认证端点返回不同的模拟响应
        if ($request_uri ~* "/session$") {
            return 200 '{"user":null}';
        }
        if ($request_uri ~* "/providers$") {
            return 200 '{}';
        }
        if ($request_uri ~* "/csrf$") {
            return 200 '{"csrfToken":"mock-csrf-token"}';
        }

        # 默认返回空对象
        return 200 '{}';
    }

    # LobeChat API路由代理 - 其他 API (包括 webapi)
    location ~* ^/chat/(api|webapi|_next/webpack-hmr) {
        # 移除/chat前缀，直接代理到LobeChat
        rewrite ^/chat/(.*)$ /$1 break;
        proxy_pass http://***************:3210;
        proxy_http_version 1.1;

        # 关键：保持Host头和添加认证信息
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # WebSocket支持 - 确保正确的升级头
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 为LobeChat添加认证信息
        proxy_set_header X-Auth-User $http_x_auth_user;
        proxy_set_header X-Auth-Email $http_x_auth_email;
        proxy_set_header X-Auth-Name $http_x_auth_name;
        proxy_set_header X-Platform-Auth "ynnx-platform";
        proxy_set_header X-Platform-Token "ynnx-chat-token-2025";

        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 3600s;
        proxy_read_timeout 3600s;

        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # LobeChat所有根路由重定向 - 修复SPA路由问题
    # 将所有LobeChat的根路由重定向到带前缀的版本
    location = /chat {
        return 301 /chat/chat;
    }

    location = /files {
        return 301 /chat/files;
    }

    location = /image {
        return 301 /chat/image;
    }

    location = /discover {
        return 301 /chat/discover;
    }

    location = /settings {
        return 301 /chat/settings/common;
    }

    # 处理 settings 子路径
    location ~* ^/settings/(.*) {
        return 301 /chat/settings/$1;
    }

    # 处理根路径
    location = /chat/ {
        return 301 /chat/chat;
    }

    # LobeChat主页面代理
    location /chat/ {
        # 移除前缀，直接代理到LobeChat
        rewrite ^/chat/(.*)$ /$1 break;
        proxy_pass http://***************:3210;
        proxy_http_version 1.1;

        # 关键：设置正确的Host和基础路径信息
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Prefix /chat;
        proxy_set_header X-Original-URI $request_uri;

        # WebSocket支持 - 确保正确的升级头
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

        # 为LobeChat添加认证信息 - 通过自定义头部传递用户信息
        proxy_set_header X-Auth-User $http_x_auth_user;
        proxy_set_header X-Auth-Email $http_x_auth_email;
        proxy_set_header X-Auth-Name $http_x_auth_name;

        # 添加平台认证标识
        proxy_set_header X-Platform-Auth "ynnx-platform";
        proxy_set_header X-Platform-Token "ynnx-chat-token-2025";

        # 超时设置 - WebSocket和长连接需要更长的超时时间
        proxy_connect_timeout 10s;
        proxy_send_timeout 3600s;
        proxy_read_timeout 3600s;

        # 启用缓冲以支持内容替换
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;

        # 激进的解决方案：注入base标签和JavaScript路由修复
        # 在<head>标签后注入base标签和路由修复脚本
        sub_filter '<head>' '<head><base href="/chat/"><script>
        // LobeChat 路由修复脚本
        (function() {
            // 拦截 pushState 和 replaceState
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;

            function fixPath(path) {
                if (typeof path === "string") {
                    // 修复 LobeChat 的主要路由
                    if (path === "/chat" || path === "/") return "/chat/chat";
                    if (path === "/files") return "/chat/files";
                    if (path === "/image") return "/chat/image";
                    if (path === "/discover") return "/chat/discover";
                    if (path === "/settings") return "/chat/settings";
                    // 如果已经有前缀，保持不变
                    if (path.startsWith("/chat/")) return path;
                    // 其他路径添加前缀
                    if (path.startsWith("/")) return "/chat" + path;
                }
                return path;
            }

            history.pushState = function(state, title, url) {
                return originalPushState.call(this, state, title, fixPath(url));
            };

            history.replaceState = function(state, title, url) {
                return originalReplaceState.call(this, state, title, fixPath(url));
            };

            // 拦截点击事件
            document.addEventListener("click", function(e) {
                const link = e.target.closest("a");
                if (link && link.href) {
                    const url = new URL(link.href);
                    if (url.origin === window.location.origin) {
                        const fixedPath = fixPath(url.pathname);
                        if (fixedPath !== url.pathname) {
                            e.preventDefault();
                            window.location.href = fixedPath + url.search + url.hash;
                        }
                    }
                }
            }, true);
        })();
        </script>';

        # 同时进行路径替换作为备用方案
        # Next.js 静态资源
        sub_filter '/_next/static/' '/chat/_next/static/';
        sub_filter '/_next/image' '/chat/_next/image';
        sub_filter '/api/' '/chat/api/';
        sub_filter '/webapi/' '/chat/webapi/';

        # LobeChat 路由重写 - 修复SPA内部导航
        sub_filter 'href="/chat"' 'href="/chat/chat"';
        sub_filter 'href="/files"' 'href="/chat/files"';
        sub_filter 'href="/image"' 'href="/chat/image"';
        sub_filter 'href="/discover"' 'href="/chat/discover"';
        sub_filter 'href="/settings"' 'href="/chat/settings"';

        # 修复JavaScript中的路由跳转
        sub_filter "'/chat'" "'/chat/chat'";
        sub_filter "'/files'" "'/chat/files'";
        sub_filter "'/image'" "'/chat/image'";
        sub_filter "'/discover'" "'/chat/discover'";
        sub_filter "'/settings'" "'/chat/settings'";

        # 修复router.push等导航
        sub_filter 'router.push("/chat")' 'router.push("/chat/chat")';
        sub_filter 'router.push("/files")' 'router.push("/chat/files")';
        sub_filter 'router.push("/image")' 'router.push("/chat/image")';
        sub_filter 'router.push("/discover")' 'router.push("/chat/discover")';
        sub_filter 'router.push("/settings")' 'router.push("/chat/settings")';

        # 各种 favicon 文件
        sub_filter '/favicon.ico' '/chat/favicon.ico';
        sub_filter '/favicon-16x16.ico' '/chat/favicon-16x16.ico';
        sub_filter '/favicon-32x32.ico' '/chat/favicon-32x32.ico';
        sub_filter '/favicon-96x96.ico' '/chat/favicon-96x96.ico';
        sub_filter '/favicon.png' '/chat/favicon.png';
        sub_filter '/favicon.svg' '/chat/favicon.svg';

        # Apple 图标文件
        sub_filter '/apple-touch-icon.png' '/chat/apple-touch-icon.png';
        sub_filter '/apple-touch-icon-57x57.png' '/chat/apple-touch-icon-57x57.png';
        sub_filter '/apple-touch-icon-60x60.png' '/chat/apple-touch-icon-60x60.png';
        sub_filter '/apple-touch-icon-72x72.png' '/chat/apple-touch-icon-72x72.png';
        sub_filter '/apple-touch-icon-76x76.png' '/chat/apple-touch-icon-76x76.png';
        sub_filter '/apple-touch-icon-114x114.png' '/chat/apple-touch-icon-114x114.png';
        sub_filter '/apple-touch-icon-120x120.png' '/chat/apple-touch-icon-120x120.png';
        sub_filter '/apple-touch-icon-144x144.png' '/chat/apple-touch-icon-144x144.png';
        sub_filter '/apple-touch-icon-152x152.png' '/chat/apple-touch-icon-152x152.png';
        sub_filter '/apple-touch-icon-180x180.png' '/chat/apple-touch-icon-180x180.png';

        # Manifest 文件
        sub_filter '/manifest.json' '/chat/manifest.json';
        sub_filter '/manifest.webmanifest' '/chat/manifest.webmanifest';
        sub_filter '/site.webmanifest' '/chat/site.webmanifest';

        # SEO 和其他文件
        sub_filter '/robots.txt' '/chat/robots.txt';
        sub_filter '/sitemap.xml' '/chat/sitemap.xml';

        # 确保替换所有出现的地方
        sub_filter_once off;
        # 只对HTML内容进行替换
        sub_filter_types text/html;

        # 安全头部设置 - 限制iframe只能在同源使用
        proxy_hide_header X-Frame-Options;
        add_header X-Frame-Options "SAMEORIGIN" always;

        # 隐藏服务器信息，防止暴露真实地址
        proxy_hide_header Server;
        proxy_hide_header X-Powered-By;

        # 对于主页面添加安全头部（但不对静态资源添加nosniff）
        add_header X-XSS-Protection "1; mode=block" always;

        # CORS支持 - 允许iframe嵌入
        add_header Access-Control-Allow-Origin "$scheme://$host" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Auth-User, X-Auth-Email, X-Auth-Name" always;
        add_header Access-Control-Allow-Credentials "true" always;

        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "$scheme://$host" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Auth-User, X-Auth-Email, X-Auth-Name" always;
            add_header Access-Control-Allow-Credentials "true" always;
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 204;
        }
    }

    # 健康检查
    location /health {
        proxy_pass http://ldap_backend/health;
        access_log off;
    }

    # 静态资源缓存
    location ~* \.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|wasm)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 主应用路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}