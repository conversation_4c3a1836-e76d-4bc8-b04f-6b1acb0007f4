# JetBrains AI Assistant安装配置指南

> **JetBrains AI Assistant** 是专为JetBrains系列IDE设计的智能编程助手，深度集成IDE功能，提供代码补全、智能重构、错误诊断等专业功能。本指南将详细介绍在各种JetBrains IDE中的安装和配置过程。

## 📋 准备工作

### 系统要求

| 项目 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **IDE版本** | 2023.2+ | 2024.1+ | 支持最新特性 |
| **Java版本** | JDK 11+ | JDK 17+ | IDE运行环境 |
| **内存** | 4GB RAM | 8GB+ RAM | 确保流畅运行 |
| **存储空间** | 200MB可用空间 | 1GB+ | 包含缓存和索引 |
| **网络环境** | 内网连接 | 稳定高速连接 | 访问AI服务 |

### 支持的IDE详情

| IDE | 版本要求 | 支持程度 | 主要功能 | 特殊说明 |
|-----|---------|----------|----------|----------|
| **IntelliJ IDEA** | 2023.2+ | ✅ 完全支持 | 全功能支持 | 旗舰版功能最完整 |
| **PyCharm** | 2023.2+ | ✅ 完全支持 | Python专项优化 | 支持数据科学工具 |
| **WebStorm** | 2023.2+ | ✅ 完全支持 | 前端开发优化 | JavaScript/TypeScript增强 |
| **Android Studio** | 2023.2+ | ✅ 完全支持 | Android开发专用 | Kotlin支持优秀 |
| **CLion** | 2023.2+ | ✅ 完全支持 | C/C++开发 | 系统编程支持 |
| **GoLand** | 2023.2+ | ✅ 完全支持 | Go语言专用 | 微服务开发优化 |
| **Rider** | 2023.2+ | ✅ 完全支持 | .NET开发 | C#/F#支持 |
| **PhpStorm** | 2023.2+ | ✅ 完全支持 | PHP Web开发 | Laravel/Symfony支持 |
| **DataGrip** | 2023.2+ | ⚠️ 部分支持 | 数据库工具 | SQL查询优化 |
| **RubyMine** | 2023.2+ | ✅ 完全支持 | Ruby/Rails开发 | Web开发支持 |

### 前置条件检查

在开始安装前，请确认：

- [ ] IDE版本满足要求（2023.2或更高）
- [ ] 已获得平台账户和API密钥
- [ ] 网络可以访问 `http://***************:4000`
- [ ] IDE运行正常，无严重性能问题
- [ ] 已备份重要的IDE配置和项目

> ⚠️ **重要提示**：建议在非生产环境中先进行测试安装，确认功能正常后再在主要开发环境中部署。

![JetBrains IDE支持](./images/jetbrains-ide-support.png)

## 📦 获取安装包

### 获取方式

#### 方式一：平台官方下载（推荐）
1. **访问下载中心**
   - 登录平台主页
   - 导航至 **"工具下载中心"** 页面

2. **定位JetBrains插件**
   - 在页面中找到 **"JetBrains AI Assistant"** 部分
   - 点击 **"安装指南"** 按钮进入详情页

3. **下载插件包**
   - 在页面顶部找到下载链接
   - 下载 `.zip` 或 `.jar` 格式的插件文件
   - 保存到易于访问的本地目录

#### 方式二：企业内部分发
某些组织可能通过内部渠道分发：

| 分发渠道 | 获取方法 | 适用场景 |
|----------|----------|----------|
| **内部工具页面** | 访问公司内部工具分发页面 | 标准企业环境 |
| **IT部门分发** | 联系IT部门或开发团队负责人 | 受管理的企业环境 |
| **企业插件仓库** | 从企业私有插件仓库下载 | 大型企业环境 |
| **版本控制系统** | 从内部Git仓库获取 | 开发团队内部 |

#### 文件验证
下载完成后，请验证文件完整性：

```bash
# 检查文件大小（应该在10-50MB之间）
ls -lh jetbrains-ai-assistant.zip

# 检查文件类型
file jetbrains-ai-assistant.zip
```

> 💡 **提示**：建议创建专门的插件文件夹管理所有JetBrains插件，便于后续更新和维护。

## 🚀 安装步骤详解

### 标准安装流程

#### 步骤1：准备IDE环境
1. **关闭重要项目**
   - 保存所有未保存的工作
   - 关闭正在运行的调试会话
   - 停止所有后台任务

2. **检查IDE状态**
   - 确认IDE运行稳定
   - 检查是否有其他插件正在安装
   - 确保有足够的磁盘空间

#### 步骤2：打开IDE设置

**方法一：菜单访问**
- **Windows/Linux**: `File` → `Settings`
- **macOS**: `IDE名称` → `Preferences`

**方法二：快捷键**
- **Windows/Linux**: `Ctrl+Alt+S`
- **macOS**: `Cmd+,`

**方法三：欢迎屏幕**
- 在IDE欢迎屏幕点击 `Configure` → `Settings`

#### 步骤3：进入插件管理

1. **导航到插件页面**
   - 在设置窗口左侧面板中找到 `Plugins`
   - 点击展开插件管理页面

2. **准备安装**
   - 点击插件页面右上角的齿轮图标 ⚙️
   - 从下拉菜单中选择 `Install Plugin from Disk...`

![插件安装界面](./images/jetbrains-plugin-install.png)

#### 步骤4：选择和安装插件

1. **文件选择**
   - 在文件选择对话框中，浏览到下载的插件文件
   - 支持的文件格式：`.zip`、`.jar`
   - 选择正确的插件文件

2. **确认安装**
   - 点击 `OK` 确认文件选择
   - IDE会验证插件文件的完整性和兼容性

3. **安装过程**
   - IDE显示安装进度条
   - 等待安装完成（通常需要30秒到2分钟）

#### 步骤5：完成安装

1. **安装确认**
   - 安装成功后会显示确认对话框
   - 插件会出现在已安装插件列表中

2. **重启IDE**
   - 点击 `Restart IDE` 按钮
   - 或手动关闭并重新启动IDE
   - 等待IDE完全加载

#### 步骤6：验证安装

重启后验证插件是否正确安装：

- [ ] 在 `Settings` → `Plugins` → `Installed` 中能找到AI Assistant
- [ ] 插件状态显示为已启用（绿色勾选）
- [ ] IDE工具栏或菜单中出现AI相关选项
- [ ] 没有错误提示或警告信息

### 高级安装选项

#### 命令行安装（适用于部分IDE）
```bash
# IntelliJ IDEA命令行安装
idea installPlugin /path/to/jetbrains-ai-assistant.zip

# 批量安装多个插件
idea installPlugin plugin1.zip plugin2.zip plugin3.zip
```

#### 配置文件安装
```xml
<!-- 在IDE配置目录的plugins.xml中添加 -->
<plugins>
  <plugin id="jetbrains-ai-assistant"
          path="/path/to/jetbrains-ai-assistant.zip"
          enabled="true" />
</plugins>
```

### 企业环境部署

#### 批量部署配置
```xml
<!-- 企业插件部署配置示例 -->
<enterprise-plugins>
  <plugin id="jetbrains-ai-assistant"
          url="http://internal-repo/jetbrains-ai-assistant.zip"
          version="1.0.0"
          mandatory="true"
          auto-update="false" />
</enterprise-plugins>
```

#### 网络部署
```bash
# 通过网络位置安装
curl -O http://internal-server/plugins/jetbrains-ai-assistant.zip
idea installPlugin jetbrains-ai-assistant.zip
```

### 安装故障排除

#### 常见安装问题

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| **文件损坏** | 安装失败，提示文件无效 | 重新下载插件文件 |
| **版本不兼容** | 提示IDE版本过低 | 升级IDE到支持版本 |
| **权限不足** | 安装被拒绝 | 以管理员权限运行IDE |
| **磁盘空间不足** | 安装中断 | 清理磁盘空间 |
| **网络问题** | 下载失败 | 检查网络连接 |

#### 安装日志检查
```bash
# 查看IDE安装日志
# Windows
%USERPROFILE%\.IntelliJIdea2024.1\system\log\idea.log

# macOS
~/Library/Logs/JetBrains/IntelliJIdea2024.1/idea.log

# Linux
~/.cache/JetBrains/IntelliJIdea2024.1/log/idea.log
```

## ⚙️ 配置设置详解

### API连接配置

#### 步骤1：获取API密钥

1. **访问密钥管理页面**
   - 登录平台主页
   - 导航至 **"API密钥管理"** 页面

2. **生成新密钥**
   - 点击 **"生成新密钥"** 按钮
   - 设置描述名称：`JetBrains AI Assistant`
   - 复制生成的API密钥并安全保存

> 🔐 **安全提醒**：API密钥是重要凭据，请妥善保管，避免泄露。

#### 步骤2：打开AI Assistant设置

安装并重启IDE后：

1. **进入设置页面**
   - `Settings/Preferences` → `Tools` → `AI Assistant`
   - 或搜索 `AI Assistant` 快速定位

2. **配置页面结构**
   - **连接设置**：API端点和认证配置
   - **功能设置**：代码补全、聊天等功能开关
   - **性能设置**：超时、缓存等性能参数
   - **隐私设置**：数据共享和隐私控制

#### 步骤3：基础连接配置

**核心配置参数：**

| 配置项 | 值 | 说明 |
|--------|----|----|
| **API端点** | `http://***************:4000` | 内网AI服务地址 |
| **模型名称** | `qwen3-235b-a22b` | 指定使用的AI模型 |
| **API密钥** | 从平台获取 | 身份验证凭据 |
| **超时时间** | 30000ms | 请求超时设置 |
| **重试次数** | 3 | 失败重试次数 |

**配置示例：**
```json
{
  "connection": {
    "apiEndpoint": "http://***************:4000",
    "model": "qwen3-235b-a22b",
    "apiKey": "your-actual-api-key-here",
    "timeout": 30000,
    "maxRetries": 3,
    "enableSSL": false,
    "validateCertificate": false
  }
}
```

#### 步骤4：验证连接

1. **测试连接**
   - 在配置页面点击 **"Test Connection"** 按钮
   - 等待连接测试结果

2. **验证结果**
   - ✅ **成功**：显示"连接成功"和模型信息
   - ❌ **失败**：检查网络、API地址和密钥

![API配置界面](./images/jetbrains-api-config.png)

### 高级配置选项

#### 代码补全配置

**基础设置：**
```json
{
  "codeCompletion": {
    "enabled": true,              // 启用代码补全
    "autoTrigger": true,          // 自动触发建议
    "delay": 500,                 // 触发延迟（毫秒）
    "maxSuggestions": 5,          // 最大建议数量
    "contextLength": 2000,        // 上下文长度
    "enableInComments": false,    // 在注释中启用
    "enableInStrings": false      // 在字符串中启用
  }
}
```

**性能优化设置：**
```json
{
  "performance": {
    "enableCaching": true,        // 启用缓存
    "cacheSize": "100MB",        // 缓存大小
    "backgroundProcessing": true, // 后台处理
    "throttleRequests": true,     // 请求节流
    "maxConcurrentRequests": 3    // 最大并发请求
  }
}
```

#### 聊天功能配置

**界面设置：**
```json
{
  "chatInterface": {
    "enabled": true,              // 启用聊天功能
    "contextAware": true,         // 上下文感知
    "maxHistory": 100,            // 最大历史记录
    "autoSave": true,             // 自动保存对话
    "showTimestamps": true,       // 显示时间戳
    "enableMarkdown": true,       // 支持Markdown渲染
    "fontSize": 14,               // 字体大小
    "theme": "auto"               // 主题：auto/light/dark
  }
}
```

**对话设置：**
```json
{
  "conversation": {
    "maxTokensPerMessage": 4000,  // 单条消息最大长度
    "temperature": 0.7,           // 创造性程度
    "enableCodeHighlight": true,  // 代码高亮
    "autoFormatCode": true,       // 自动格式化代码
    "enableFileReferences": true  // 启用文件引用
  }
}
```

#### 隐私和安全配置

**数据处理设置：**
```json
{
  "privacy": {
    "sendCodeContext": true,      // 发送代码上下文
    "sendFileNames": true,        // 发送文件名
    "sendProjectStructure": false, // 发送项目结构
    "logInteractions": false,     // 记录交互日志
    "shareUsageStats": false,     // 共享使用统计
    "encryptCommunication": true, // 加密通信
    "anonymizeData": true         // 数据匿名化
  }
}
```

**安全策略：**
```json
{
  "security": {
    "enableContentFilter": true,  // 启用内容过滤
    "blockSensitiveData": true,   // 阻止敏感数据
    "validateResponses": true,    // 验证响应内容
    "auditTrail": false,          // 审计跟踪
    "sessionTimeout": 3600        // 会话超时（秒）
  }
}
```

#### IDE集成配置

**编辑器集成：**
```json
{
  "editorIntegration": {
    "enableInlineCompletion": true,    // 内联补全
    "enableContextMenu": true,         // 右键菜单
    "enableToolbar": true,             // 工具栏按钮
    "enableStatusBar": true,           // 状态栏显示
    "enableGutterIcons": false,        // 装订线图标
    "highlightSuggestions": true       // 高亮建议
  }
}
```

**快捷键配置：**
```json
{
  "shortcuts": {
    "openChat": "Ctrl+Shift+A",        // 打开聊天
    "triggerCompletion": "Ctrl+Space", // 触发补全
    "explainCode": "Ctrl+Shift+E",     // 解释代码
    "generateTests": "Ctrl+Shift+T",   // 生成测试
    "refactorCode": "Ctrl+Shift+R"     // 重构代码
  }
}
```

## 💡 首次使用指南

### 启动AI助手

#### 多种启动方式

**方法一：工具窗口（推荐）**
1. **查找AI Chat窗口**
   - 在IDE底部工具栏寻找 `AI Chat` 标签
   - 如果没有显示，右键点击工具栏空白处
   - 选择 `AI Chat` 显示工具窗口

2. **手动打开工具窗口**
   - 菜单：`View` → `Tool Windows` → `AI Chat`
   - 或在 `View` → `Tool Windows` → `More` 中查找

**方法二：快捷键访问**
- **默认快捷键**：
  - Windows/Linux: `Ctrl+Shift+A`
  - macOS: `Cmd+Shift+A`
- **自定义快捷键**：
  - `Settings` → `Keymap` → 搜索 "AI Assistant"
  - 设置个人偏好的快捷键组合

**方法三：菜单访问**
- `Tools` → `AI Assistant` → `Open Chat`
- `Tools` → `AI Assistant` → `Code Completion`

**方法四：右键菜单**
- 在代码编辑器中右键
- 选择 `AI Assistant` 子菜单
- 选择具体功能（解释代码、生成测试等）

### 界面介绍

#### AI Chat工具窗口布局

```
┌─────────────────────────────────────┐
│ AI Chat                    [⚙️] [×] │
├─────────────────────────────────────┤
│ 🟢 已连接 qwen3-235b-a22b          │
├─────────────────────────────────────┤
│                                     │
│ 对话历史区域                        │
│                                     │
│ 用户: 你好                          │
│ AI: 你好！我可以帮助您...           │
│                                     │
├─────────────────────────────────────┤
│ [📎] 输入您的问题...        [发送] │
└─────────────────────────────────────┘
```

**界面元素说明：**
- **状态指示器**：显示连接状态和当前模型
- **对话区域**：显示历史对话记录
- **输入框**：输入问题和指令
- **附件按钮**：添加文件或代码片段
- **设置按钮**：快速访问配置选项

### 基本使用流程

#### 步骤1：验证连接状态

首次启动时，AI助手会自动进行连接验证：

**成功状态显示：**
```
✅ 连接成功 - qwen3-235b-a22b 模型可用
🔧 配置完成 - 代码补全已启用
💬 聊天就绪 - 可以开始对话
📊 API配额 - 剩余调用次数: 9,876
```

**失败状态处理：**
```
❌ 连接失败 - 请检查网络和配置
🔧 配置问题 - 点击设置按钮进行配置
⚠️ 模型不可用 - 请联系管理员
```

#### 步骤2：功能测试

**基础对话测试：**
```
用户：你好，请介绍一下你的功能

AI助手回复：
你好！我是JetBrains AI助手，专为提升您的开发效率而设计。

🔧 主要功能：
• 智能代码生成和补全
• 代码分析和重构建议
• 错误诊断和修复方案
• 技术问题解答和指导
• 代码文档和注释生成
• 单元测试自动生成

💡 使用技巧：
• 选中代码后右键使用AI功能
• 在聊天中描述需求获得代码
• 使用@符号引用项目文件
• 询问最佳实践和设计模式

准备好开始编程了吗？
```

#### 步骤3：代码功能测试

**代码解释功能：**
1. 在编辑器中选中一段代码
2. 右键选择 `AI Assistant` → `Explain Code`
3. 查看AI的详细解释

**代码生成功能：**
```
用户：请创建一个Java单例模式的示例

AI：我来为您创建一个线程安全的单例模式示例：

```java
public class Singleton {
    // 使用volatile确保多线程环境下的可见性
    private static volatile Singleton instance;

    // 私有构造函数防止外部实例化
    private Singleton() {
        // 防止反射攻击
        if (instance != null) {
            throw new RuntimeException("Use getInstance() method");
        }
    }

    // 双重检查锁定模式
    public static Singleton getInstance() {
        if (instance == null) {
            synchronized (Singleton.class) {
                if (instance == null) {
                    instance = new Singleton();
                }
            }
        }
        return instance;
    }
}
```

这个实现使用了双重检查锁定模式，确保线程安全且性能优秀。
```

#### 步骤4：高级功能探索

**文件引用功能：**
```
用户：请分析@UserService.java中的代码结构

AI：我来分析UserService.java的代码结构...
[AI会读取并分析指定文件]
```

**项目上下文功能：**
```
用户：基于当前项目结构，建议如何组织新的用户管理模块

AI：基于您的项目结构分析，我建议...
[AI会考虑项目的整体架构]
```

![使用示例](./images/jetbrains-usage-example.png)

## 🔧 功能验证与测试

### 代码补全功能测试

#### Java代码补全示例
```java
// 在Java文件中输入以下代码片段
public class Calculator {
    public int add(int a, int b) {
        // 输入 "ret" 后，AI应该建议：return a + b;
    }

    public List<String> getUsers() {
        // 输入 "List<String> users = " 后，AI应该建议初始化代码
    }
}
```

#### Python代码补全示例
```python
# 在Python文件中测试
def fibonacci(n):
    # 输入 "if n <= 1:" 后，AI应该建议完整的斐波那契实现

class UserManager:
    def __init__(self):
        # AI应该建议初始化代码
```

#### JavaScript/TypeScript补全示例
```typescript
// 在TypeScript文件中测试
interface User {
    id: number;
    name: string;
    email: string;
}

function createUser(userData: Partial<User>): User {
    // AI应该建议完整的用户创建逻辑
}
```

### 聊天功能全面测试

#### 基础对话测试
```
测试用例1 - 代码解释：
用户：请解释这个函数的作用
[选中一段代码]

测试用例2 - 代码生成：
用户：创建一个RESTful API的用户登录接口，使用Spring Boot

测试用例3 - 调试帮助：
用户：为什么我的代码出现NullPointerException？
[提供错误代码片段]

测试用例4 - 最佳实践：
用户：在Java中实现单例模式的最佳方式是什么？

测试用例5 - 代码审查：
用户：请审查这段代码并提出改进建议
[提供代码片段]
```

### 右键菜单功能测试

#### 代码分析功能
1. **解释代码**
   - 选中复杂的算法代码
   - 右键 → `AI Assistant` → `Explain Code`
   - 验证AI是否提供清晰的解释

2. **生成测试**
   - 选中一个方法
   - 右键 → `AI Assistant` → `Generate Tests`
   - 检查生成的单元测试质量

3. **重构建议**
   - 选中重复或复杂的代码
   - 右键 → `AI Assistant` → `Suggest Refactoring`
   - 评估重构建议的合理性

#### 文档生成功能
```java
// 选中这个方法
public boolean validateEmail(String email) {
    return email != null && email.matches("^[A-Za-z0-9+_.-]+@(.+)$");
}
// 右键 → AI Assistant → Generate Documentation
// 应该生成完整的JavaDoc注释
```

### 性能和响应测试

#### 响应时间测试
- **代码补全响应**：应在500ms内显示建议
- **聊天回复速度**：简单问题应在3秒内回复
- **代码分析速度**：中等复杂度代码应在5秒内完成分析

#### 并发处理测试
- 同时进行代码补全和聊天对话
- 验证功能是否正常工作
- 检查是否有性能下降

## 📹 安装演示视频

<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/jetbrains-installation-guide.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>

## 🔧 故障排除与常见问题

### 安装相关问题

#### Q: 插件安装后在IDE中找不到？
**系统性排查步骤：**

1. **验证安装状态**
   - `Settings` → `Plugins` → `Installed`
   - 确认AI Assistant显示在列表中
   - 检查插件状态是否为"已启用"

2. **检查IDE兼容性**
   - 确保IDE版本 ≥ 2023.2
   - 在 `Help` → `About` 中查看版本信息
   - 必要时升级IDE到支持版本

3. **查看错误日志**
   - `Help` → `Show Log in Explorer/Finder`
   - 搜索与AI Assistant相关的错误信息
   - 检查是否有插件加载失败的记录

4. **重新安装插件**
   - 卸载现有插件
   - 重新下载最新版本
   - 重新安装并重启IDE

#### Q: 插件安装过程中出现错误？
**常见错误和解决方案：**

| 错误类型 | 错误信息 | 解决方案 |
|----------|----------|----------|
| **文件损坏** | "Plugin file is corrupted" | 重新下载插件文件 |
| **版本不兼容** | "Plugin is not compatible" | 升级IDE或下载兼容版本 |
| **权限不足** | "Access denied" | 以管理员权限运行IDE |
| **磁盘空间** | "Not enough disk space" | 清理磁盘空间 |

### 配置相关问题

#### Q: API连接失败？
**详细排查流程：**

1. **网络连通性测试**
   ```bash
   # 测试服务器连通性
   ping ***************

   # 测试端口可达性
   telnet *************** 4000

   # HTTP请求测试
   curl -I http://***************:4000
   ```

2. **配置参数验证**
   - **API地址**：确认使用 `http://***************:4000`
   - **模型名称**：确认使用 `qwen3-235b-a22b`
   - **API密钥**：验证密钥格式和有效性
   - **网络代理**：检查IDE代理设置

3. **防火墙和安全设置**
   - 检查企业防火墙规则
   - 确认端口4000未被阻止
   - 验证SSL/TLS设置

#### Q: 代码补全功能不工作？
**功能诊断步骤：**

1. **功能开关检查**
   - `Settings` → `AI Assistant` → `Code Completion`
   - 确保"启用代码补全"选项已勾选
   - 检查触发延迟设置（建议500ms）

2. **插件冲突排查**
   - 禁用其他AI代码补全插件
   - 检查是否与Copilot等插件冲突
   - 逐一禁用可疑插件进行测试

3. **项目类型支持**
   - 确认当前项目类型被支持
   - 检查文件类型是否在支持列表中
   - 验证项目SDK配置正确

### 性能相关问题

#### Q: IDE运行缓慢或卡顿？
**性能优化方案：**

1. **IDE内存配置**
   ```properties
   # 在IDE的自定义VM选项中添加：
   -Xms2g                    # 初始堆内存
   -Xmx8g                    # 最大堆内存
   -XX:ReservedCodeCacheSize=1g  # 代码缓存
   -XX:+UseG1GC              # 使用G1垃圾收集器
   -XX:MaxGCPauseMillis=200  # 最大GC暂停时间
   ```

2. **插件性能配置**
   ```json
   {
     "performance": {
       "enableParallelProcessing": true,   // 并行处理
       "cacheSize": "500MB",              // 缓存大小
       "backgroundProcessing": true,       // 后台处理
       "throttleRequests": true,          // 请求节流
       "maxConcurrentRequests": 2,        // 最大并发请求
       "enableLazyLoading": true          // 懒加载
     }
   }
   ```

3. **系统资源监控**
   - 使用IDE内置的性能监控工具
   - 监控CPU和内存使用情况
   - 定期清理IDE缓存和索引

### 使用相关问题

#### Q: 如何完全卸载插件？
**完整卸载步骤：**

1. **通过IDE卸载**
   - `Settings` → `Plugins` → `Installed`
   - 找到AI Assistant插件
   - 点击下拉箭头 → `Uninstall`

2. **清理残留文件**
   ```bash
   # Windows
   rmdir /s "%USERPROFILE%\.IntelliJIdea2024.1\config\plugins\ai-assistant"

   # macOS
   rm -rf ~/Library/Application\ Support/JetBrains/IntelliJIdea2024.1/plugins/ai-assistant

   # Linux
   rm -rf ~/.config/JetBrains/IntelliJIdea2024.1/plugins/ai-assistant
   ```

3. **重启IDE**
   - 完全关闭IDE
   - 重新启动验证插件已完全移除

## 📊 使用监控与统计

### 插件状态监控

#### 实时状态查看
在IDE中查看AI助手实时状态：
- `Help` → `AI Assistant Status`
- 或在状态栏点击AI图标

**状态信息包括：**
```
🔗 连接状态: 已连接
🤖 当前模型: qwen3-235b-a22b
📊 API配额: 剩余 8,765 次调用
⏱️ 平均响应时间: 1.2秒
📈 成功率: 96.8%
🔄 最后同步: 2分钟前
```

#### 使用统计报告
```
📊 本月使用统计：
┌─────────────────┬─────────┐
│ 功能            │ 使用次数 │
├─────────────────┼─────────┤
│ 代码补全        │ 5,678   │
│ 聊天对话        │ 234     │
│ 代码解释        │ 156     │
│ 测试生成        │ 89      │
│ 重构建议        │ 67      │
│ 文档生成        │ 45      │
└─────────────────┴─────────┘

总体满意度: ⭐⭐⭐⭐⭐ (4.8/5.0)
```

### 性能指标监控

#### 关键性能指标
- **响应时间**：平均API响应时间
- **成功率**：请求成功率
- **缓存命中率**：本地缓存效率
- **内存使用**：插件内存占用

## 📚 相关资源

- [JetBrains AI Assistant功能详解](./features.md)
- [API密钥管理指南](../faq/common-faq.md#api密钥相关)
- [平台工具下载中心](../../)

---

*最后更新时间：2025年1月 | 版本：v2.0*