# RooCode多模式使用指南

> **RooCode的核心优势**在于其独特的四模式设计：Code、Architect、Ask、Debug。每种模式都针对特定的开发场景进行了深度优化，让AI助手能够在不同的工作阶段提供最专业的支持。

## 📚 模式概览

### 四种模式对比

| 模式 | 图标 | 核心功能 | 主要用途 | 输出类型 | 交互方式 |
|------|------|----------|----------|----------|----------|
| **Code** | 🔨 | 代码实现 | 编程开发 | 代码文件 | 任务导向 |
| **Architect** | 🏗️ | 架构设计 | 系统规划 | 设计方案 | 讨论式 |
| **Ask** | ❓ | 知识咨询 | 学习答疑 | 知识解答 | 问答式 |
| **Debug** | 🐛 | 问题诊断 | 故障排除 | 解决方案 | 分析式 |

### 模式选择决策树

```mermaid
graph TD
    A[开始] --> B{需要什么帮助？}
    B -->|编写代码| C[Code模式 🔨]
    B -->|设计架构| D[Architect模式 🏗️]
    B -->|学习知识| E[Ask模式 ❓]
    B -->|解决问题| F[Debug模式 🐛]

    C --> C1[生成代码文件]
    C --> C2[执行命令]
    C --> C3[修改文件]

    D --> D1[架构建议]
    D --> D2[技术选型]
    D --> D3[设计方案]

    E --> E1[概念解释]
    E --> E2[最佳实践]
    E --> E3[学习指导]

    F --> F1[问题分析]
    F --> F2[调试方案]
    F --> F3[修复建议]
```

## 🔨 Code模式 - 编程实现专家

### 核心特性

Code模式是RooCode的旗舰功能，专为实际编程工作设计，具备完整的开发能力。

#### 主要功能矩阵

| 功能类别 | 具体能力 | 使用场景 |
|----------|----------|----------|
| **代码生成** | 智能补全、函数生成、类创建 | 新功能开发 |
| **文件操作** | 创建、编辑、删除、重构 | 项目管理 |
| **测试支持** | 单元测试、集成测试生成 | 质量保证 |
| **命令执行** | 构建、部署、包管理 | 开发流程 |
| **浏览器自动化** | 页面操作、截图、测试 | E2E测试 |
| **错误修复** | 语法错误、逻辑错误检测 | 代码维护 |

### 实战应用场景

#### 场景1：全栈功能开发
```
请帮我实现一个完整的用户注册功能，包括：

前端要求：
- React + TypeScript
- 表单验证（邮箱、密码强度）
- 用户反馈（成功/错误提示）
- 响应式设计

后端要求：
- Node.js + Express
- 数据验证和清理
- 密码加密存储
- 邮箱验证机制

数据库：
- MongoDB用户模型
- 索引优化
- 数据迁移脚本
```

#### 场景2：性能优化实现
```
我的React应用渲染缓慢，请帮我：

1. 分析当前组件结构
2. 实现React.memo优化
3. 添加useMemo和useCallback
4. 实现虚拟滚动
5. 代码分割和懒加载
6. 添加性能监控代码
```

### 高级使用技巧

#### 上下文感知编程
```
基于我的项目结构：
- src/components/ (React组件)
- src/hooks/ (自定义Hooks)
- src/utils/ (工具函数)
- src/types/ (TypeScript类型)

请创建一个用户管理模块，遵循现有的代码风格和架构模式。
```

#### 增量开发模式
```
第一步：创建基础的用户列表组件
第二步：添加搜索和过滤功能
第三步：实现分页和排序
第四步：添加批量操作功能
第五步：集成单元测试
```

### 代码示例：完整的登录组件

```typescript
// 用户输入：创建一个企业级的用户登录组件
// RooCode Code模式生成的完整实现

import React, { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAuth } from '../hooks/useAuth';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Alert } from '../components/ui/Alert';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

// 表单验证模式
const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少6位字符'),
  remember: z.boolean().optional()
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  redirectTo = '/dashboard'
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login, error: authError, clearError } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: 'onChange'
  });

  // 清除错误信息
  useEffect(() => {
    if (authError) {
      const timer = setTimeout(clearError, 5000);
      return () => clearTimeout(timer);
    }
  }, [authError, clearError]);

  const onSubmit = useCallback(async (data: LoginFormData) => {
    try {
      setIsSubmitting(true);
      await login(data.email, data.password, data.remember);
      onSuccess?.();
      // 登录成功后重定向
      window.location.href = redirectTo;
    } catch (error) {
      // 错误已由useAuth处理
      console.error('Login failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [login, onSuccess, redirectTo]);

  return (
    <div className="w-full max-w-md mx-auto">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900">登录账户</h2>
          <p className="mt-2 text-gray-600">请输入您的登录凭据</p>
        </div>

        {/* 错误提示 */}
        {authError && (
          <Alert variant="error" className="mb-4">
            {authError}
          </Alert>
        )}

        {/* 邮箱输入 */}
        <div>
          <Input
            {...register('email')}
            type="email"
            label="邮箱地址"
            placeholder="<EMAIL>"
            error={errors.email?.message}
            autoComplete="email"
            autoFocus
          />
        </div>

        {/* 密码输入 */}
        <div>
          <Input
            {...register('password')}
            type="password"
            label="密码"
            placeholder="请输入密码"
            error={errors.password?.message}
            autoComplete="current-password"
          />
        </div>

        {/* 记住我选项 */}
        <div className="flex items-center justify-between">
          <label className="flex items-center">
            <input
              {...register('remember')}
              type="checkbox"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700">记住我</span>
          </label>

          <a
            href="/forgot-password"
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            忘记密码？
          </a>
        </div>

        {/* 提交按钮 */}
        <Button
          type="submit"
          disabled={!isValid || isSubmitting}
          className="w-full"
          size="lg"
        >
          {isSubmitting ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              登录中...
            </>
          ) : (
            '登录'
          )}
        </Button>

        {/* 注册链接 */}
        <div className="text-center">
          <span className="text-gray-600">还没有账户？</span>
          <a
            href="/register"
            className="ml-1 text-blue-600 hover:text-blue-500 font-medium"
          >
            立即注册
          </a>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;
```

![Code模式界面](./images/roocode-code-mode.png)

## 🏗️ Architect模式 - 系统架构师

### 核心理念

Architect模式扮演资深系统架构师的角色，专注于高层次的系统设计和技术决策，避免陷入具体实现细节。

#### 设计哲学
- **战略思维**：从业务需求出发，制定技术战略
- **全局视角**：考虑系统的整体性和一致性
- **前瞻性规划**：预见未来的扩展和演进需求
- **权衡决策**：在性能、成本、复杂度间找到平衡

### 核心能力矩阵

| 能力领域 | 具体功能 | 输出成果 |
|----------|----------|----------|
| **系统设计** | 架构模式、组件划分、接口设计 | 架构图、设计文档 |
| **技术选型** | 框架对比、工具评估、平台选择 | 技术决策报告 |
| **性能规划** | 容量规划、性能预测、瓶颈分析 | 性能规划方案 |
| **安全架构** | 安全策略、风险评估、合规设计 | 安全架构方案 |
| **可扩展性** | 水平扩展、垂直扩展、弹性设计 | 扩展性规划 |
| **集成设计** | 系统集成、API设计、数据流 | 集成架构图 |

### 设计约束与原则

#### 功能边界
- ✅ **可以做的**：
  - 提供架构设计建议和方案
  - 分析技术选型的优缺点
  - 制定系统集成策略
  - 评估性能和安全风险
  - 规划系统演进路径

- ❌ **不能做的**：
  - 编写具体的代码实现
  - 执行系统命令或脚本
  - 直接操作文件和数据库
  - 进行具体的配置操作

#### 设计原则
1. **单一职责**：每个组件只负责一个明确的功能
2. **开闭原则**：对扩展开放，对修改封闭
3. **依赖倒置**：依赖抽象而不是具体实现
4. **接口隔离**：使用专门的接口而不是通用接口
5. **最小知识**：组件间保持最小的耦合度

### 实战应用场景

#### 场景1：电商平台架构设计
```
需求：设计一个支持百万用户的电商平台

业务要求：
- 用户注册、登录、个人中心
- 商品浏览、搜索、推荐
- 购物车、订单、支付
- 库存管理、物流跟踪
- 商家入驻、店铺管理

技术要求：
- 高并发：支持10万QPS
- 高可用：99.9%可用性
- 可扩展：支持业务快速增长
- 安全性：用户数据和交易安全
- 成本控制：合理的运营成本

请提供完整的系统架构方案。
```

**Architect模式回复示例：**

#### 电商平台系统架构方案

##### 1. 整体架构设计

```mermaid
graph TB
    subgraph "用户层"
        Web[Web应用]
        Mobile[移动应用]
        Admin[管理后台]
    end

    subgraph "接入层"
        CDN[CDN]
        LB[负载均衡器]
        Gateway[API网关]
    end

    subgraph "应用层"
        UserSvc[用户服务]
        ProductSvc[商品服务]
        OrderSvc[订单服务]
        PaymentSvc[支付服务]
        InventorySvc[库存服务]
        NotificationSvc[通知服务]
    end

    subgraph "数据层"
        UserDB[(用户数据库)]
        ProductDB[(商品数据库)]
        OrderDB[(订单数据库)]
        Cache[(Redis缓存)]
        Search[(搜索引擎)]
        MQ[消息队列]
    end

    Web --> CDN
    Mobile --> CDN
    Admin --> CDN
    CDN --> LB
    LB --> Gateway
    Gateway --> UserSvc
    Gateway --> ProductSvc
    Gateway --> OrderSvc
    Gateway --> PaymentSvc
    Gateway --> InventorySvc
    Gateway --> NotificationSvc

    UserSvc --> UserDB
    UserSvc --> Cache
    ProductSvc --> ProductDB
    ProductSvc --> Search
    OrderSvc --> OrderDB
    OrderSvc --> MQ
    PaymentSvc --> MQ
    InventorySvc --> Cache
    NotificationSvc --> MQ
```

##### 2. 技术选型建议

| 层级 | 组件 | 推荐技术 | 理由 |
|------|------|----------|------|
| **前端** | Web应用 | React + Next.js | SSR支持，SEO友好 |
| **前端** | 移动应用 | React Native | 跨平台，代码复用 |
| **网关** | API网关 | Kong/Nginx Plus | 高性能，插件丰富 |
| **后端** | 微服务框架 | Node.js + Express | 开发效率高，生态丰富 |
| **数据库** | 主数据库 | PostgreSQL | ACID支持，性能优秀 |
| **缓存** | 缓存系统 | Redis Cluster | 高可用，数据持久化 |
| **搜索** | 搜索引擎 | Elasticsearch | 全文搜索，实时分析 |
| **消息** | 消息队列 | Apache Kafka | 高吞吐，持久化 |
| **监控** | 监控系统 | Prometheus + Grafana | 开源，功能完整 |

##### 3. 核心设计决策

**微服务拆分策略：**
- 按业务领域拆分（DDD原则）
- 每个服务独立数据库
- 通过API和消息队列通信
- 服务间松耦合，高内聚

**数据一致性方案：**
- 强一致性：用户账户、支付交易
- 最终一致性：库存更新、订单状态
- 分布式事务：Saga模式
- 数据同步：事件驱动架构

**性能优化策略：**
- 多级缓存：CDN + Redis + 应用缓存
- 数据库优化：读写分离、分库分表
- 异步处理：消息队列解耦
- 静态资源：CDN加速

##### 4. 扩展性规划

**水平扩展：**
- 无状态服务设计
- 容器化部署（Docker + Kubernetes）
- 自动伸缩策略
- 数据库分片

**垂直扩展：**
- 服务拆分细化
- 专用硬件优化
- 缓存层级优化
- 数据库性能调优

#### 场景2：遗留系统现代化改造
```
现状：10年历史的单体Java应用
问题：维护困难、扩展性差、技术债务重
目标：微服务化改造，提升开发效率

请提供渐进式改造方案。
```

### 架构评估框架

#### 质量属性评估

| 质量属性 | 评估维度 | 评估方法 |
|----------|----------|----------|
| **性能** | 响应时间、吞吐量、资源利用率 | 性能测试、容量规划 |
| **可用性** | 故障恢复、服务降级、监控告警 | 故障演练、SLA定义 |
| **可扩展性** | 水平扩展、垂直扩展、弹性伸缩 | 压力测试、扩展验证 |
| **安全性** | 身份认证、数据加密、访问控制 | 安全审计、渗透测试 |
| **可维护性** | 代码质量、文档完整性、测试覆盖 | 代码审查、技术债务评估 |

![Architect模式分析](./images/roocode-architect-mode.png)

## ❓ Ask模式 - 技术顾问专家

### 核心定位

Ask模式扮演资深技术顾问的角色，专注于知识传授、概念解释和最佳实践指导，是您的私人技术导师。

#### 咨询理念
- **深度解释**：不仅告诉你"是什么"，更解释"为什么"
- **实践导向**：结合实际项目经验提供建议
- **循序渐进**：根据您的技术水平调整解释深度
- **全面视角**：从多个角度分析技术选择

### 专业领域覆盖

| 领域 | 咨询内容 | 服务深度 |
|------|----------|----------|
| **编程语言** | 语法特性、设计模式、最佳实践 | 从入门到精通 |
| **框架技术** | 框架对比、使用场景、迁移策略 | 深度技术分析 |
| **系统设计** | 架构模式、设计原则、权衡决策 | 企业级经验 |
| **工程实践** | 代码质量、测试策略、CI/CD | 团队协作经验 |
| **性能优化** | 性能分析、优化策略、监控方案 | 实战经验分享 |
| **职业发展** | 学习路径、技能提升、职业规划 | 行业洞察 |

### 功能边界

#### 专注领域
- ✅ **概念解释**：深入浅出地解释技术概念
- ✅ **最佳实践**：分享行业认可的最佳实践
- ✅ **技术对比**：客观分析不同技术方案
- ✅ **学习指导**：提供结构化的学习路径
- ✅ **代码审查**：从设计角度评估代码质量
- ✅ **职业建议**：技术职业发展指导

#### 功能限制
- ❌ **不编写代码**：专注于知识传授而非实现
- ❌ **不执行命令**：避免干扰学习过程
- ❌ **不操作文件**：保持纯粹的咨询角色

### 实战咨询案例

#### 案例1：React Hooks深度解析
```
用户：我刚开始学React，听说Hooks很重要，但不太理解它的价值。
能详细解释一下React Hooks吗？
```

**Ask模式专业回复：**

#### React Hooks全面解析

##### 1. 什么是React Hooks？

React Hooks是React 16.8引入的革命性特性，它们是一组特殊的函数，让你能在函数组件中"钩入"React的状态管理和生命周期特性。

**核心理念：**
- **函数式编程**：拥抱函数式组件的简洁性
- **逻辑复用**：通过自定义Hook实现逻辑共享
- **关注分离**：将相关逻辑组织在一起

##### 2. 为什么需要Hooks？

**传统类组件的痛点：**
```javascript
// 类组件的复杂性示例
class UserProfile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      user: null,
      loading: true,
      error: null
    };
  }

  componentDidMount() {
    this.fetchUser();
    this.setupEventListeners();
  }

  componentWillUnmount() {
    this.cleanupEventListeners();
  }

  // 逻辑分散在不同的生命周期方法中
  // this绑定问题
  // 难以测试和复用
}
```

**Hooks的优势：**
```javascript
// 使用Hooks的简洁实现
function UserProfile() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // 相关逻辑组织在一起
    fetchUser();
    setupEventListeners();

    return () => {
      cleanupEventListeners();
    };
  }, []);

  // 逻辑清晰，易于理解和测试
}
```

##### 3. 核心Hooks详解

| Hook | 用途 | 使用场景 | 最佳实践 |
|------|------|----------|----------|
| `useState` | 状态管理 | 组件内部状态 | 状态拆分，避免复杂对象 |
| `useEffect` | 副作用处理 | 数据获取，订阅 | 依赖数组优化，清理函数 |
| `useContext` | 上下文访问 | 跨组件数据共享 | 避免过度使用，性能考虑 |
| `useMemo` | 计算缓存 | 昂贵计算优化 | 合理使用，避免过度优化 |
| `useCallback` | 函数缓存 | 函数引用稳定 | 配合memo使用 |
| `useRef` | 引用管理 | DOM访问，值保持 | 不触发重渲染 |

##### 4. 自定义Hook最佳实践

```javascript
// 优秀的自定义Hook示例
function useApi(url) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let cancelled = false;

    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch(url);
        const result = await response.json();

        if (!cancelled) {
          setData(result);
          setError(null);
        }
      } catch (err) {
        if (!cancelled) {
          setError(err);
        }
      } finally {
        if (!cancelled) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      cancelled = true;
    };
  }, [url]);

  return { data, loading, error };
}
```

##### 5. 学习路径建议

**初级阶段（1-2周）：**
1. 掌握useState和useEffect基础用法
2. 理解依赖数组的作用
3. 练习简单的状态管理

**中级阶段（2-4周）：**
1. 学习useContext、useMemo、useCallback
2. 开始编写自定义Hook
3. 理解性能优化原理

**高级阶段（1-2个月）：**
1. 掌握useReducer复杂状态管理
2. 深入理解Hook的执行机制
3. 设计可复用的Hook库

#### 案例2：技术选型咨询
```
用户：我们团队要开发一个中型Web应用，在React、Vue、Angular之间犹豫，
能帮我分析一下各自的优缺点吗？
```

**Ask模式对比分析：**

#### 前端框架技术选型分析

##### 框架对比矩阵

| 维度 | React | Vue | Angular |
|------|-------|-----|---------|
| **学习曲线** | 中等 | 较低 | 较高 |
| **生态系统** | 最丰富 | 丰富 | 完整 |
| **性能** | 优秀 | 优秀 | 良好 |
| **企业支持** | Facebook | 社区驱动 | Google |
| **TypeScript** | 良好支持 | 良好支持 | 原生支持 |
| **移动端** | React Native | NativeScript | Ionic |
| **团队规模** | 适合各种规模 | 适合中小团队 | 适合大型团队 |

##### 选择建议

**选择React如果：**
- 团队有React经验
- 需要丰富的第三方库
- 计划开发移动应用
- 重视灵活性和自由度

**选择Vue如果：**
- 团队技术水平参差不齐
- 需要快速上手和开发
- 项目规模中等
- 重视开发体验

**选择Angular如果：**
- 大型企业级应用
- 团队有Java/.NET背景
- 需要完整的解决方案
- 重视代码规范和架构

## 🐛 Debug模式 - 问题诊断专家

### 核心使命

Debug模式是您的专业调试顾问，采用系统化的方法论进行问题诊断，从症状分析到根因定位，再到解决方案制定，提供全流程的调试支持。

#### 调试哲学
- **科学方法**：基于证据的系统性分析
- **结构化思维**：遵循标准的调试流程
- **全面诊断**：从多个维度分析问题
- **预防导向**：不仅解决问题，更要防止复发

### 专业调试能力

| 调试领域 | 核心能力 | 分析深度 |
|----------|----------|----------|
| **错误诊断** | 异常分析、堆栈追踪、错误分类 | 根因分析 |
| **性能调试** | 性能瓶颈、内存泄漏、渲染优化 | 性能剖析 |
| **网络问题** | 请求分析、连接问题、超时诊断 | 网络层面 |
| **兼容性** | 浏览器差异、版本兼容、环境问题 | 跨平台分析 |
| **集成调试** | API集成、第三方服务、数据流 | 系统集成 |
| **部署问题** | 环境配置、依赖冲突、版本管理 | 运维层面 |

### 标准调试流程

#### 六步调试法

```mermaid
graph LR
    A[1. 问题识别] --> B[2. 信息收集]
    B --> C[3. 假设分析]
    C --> D[4. 验证测试]
    D --> E[5. 解决实施]
    E --> F[6. 预防措施]
    F --> G[问题解决]

    D --> C
    C --> B
```

1. **问题识别**：准确描述问题现象和影响范围
2. **信息收集**：收集日志、错误信息、环境数据
3. **假设分析**：基于经验和数据提出可能原因
4. **验证测试**：通过实验验证或排除假设
5. **解决实施**：实施最优解决方案
6. **预防措施**：建立机制防止问题复发

### 实战调试案例

#### 案例1：React应用性能问题
```
问题报告：
我们的React电商应用在生产环境中首次加载时间超过10秒，
用户体验很差，但在开发环境中一切正常。

环境信息：
- React 18 + Vite
- 部署在AWS CloudFront
- 用户主要在移动端访问
- 问题出现在最近的版本发布后
```

**Debug模式系统分析：**

#### 性能问题诊断报告

##### 1. 问题识别与分类

**问题描述：**
- **症状**：首次加载时间 > 10秒
- **环境**：生产环境特有问题
- **影响范围**：所有用户，移动端更严重
- **时间点**：最近版本发布后出现

**问题分类：**
- 主要类型：性能问题
- 子类型：首次加载性能
- 严重程度：高（影响用户体验）
- 紧急程度：高（影响业务）

##### 2. 信息收集清单

**需要收集的关键信息：**

| 信息类型 | 具体内容 | 收集方法 |
|----------|----------|----------|
| **网络数据** | 资源加载时间、请求大小 | Chrome DevTools Network |
| **Bundle分析** | 代码包大小、依赖分析 | webpack-bundle-analyzer |
| **性能指标** | FCP、LCP、CLS、FID | Lighthouse、Web Vitals |
| **服务器日志** | 响应时间、错误率 | 服务器监控 |
| **用户环境** | 设备类型、网络状况 | Analytics数据 |

**诊断命令：**
```bash
# 1. 分析Bundle大小
npx webpack-bundle-analyzer build/static/js/*.js

# 2. 性能审计
npx lighthouse https://your-app.com --output=json

# 3. 网络分析
curl -w "@curl-format.txt" -o /dev/null -s https://your-app.com
```

##### 3. 假设分析与验证

**假设1：Bundle体积过大**
```
验证方法：
- 检查main.js文件大小
- 分析第三方依赖占比
- 对比历史版本的Bundle大小

预期发现：
- Bundle > 1MB（移动端不友好）
- 包含未使用的依赖
- 缺少代码分割
```

**假设2：CDN配置问题**
```
验证方法：
- 检查CloudFront缓存策略
- 测试不同地区的访问速度
- 分析缓存命中率

预期发现：
- 缓存策略不当
- 某些资源未被缓存
- CDN节点覆盖不足
```

**假设3：资源加载策略问题**
```
验证方法：
- 检查资源加载顺序
- 分析关键渲染路径
- 测试资源预加载效果

预期发现：
- 关键资源加载延迟
- 缺少资源优先级设置
- 未使用预加载策略
```

##### 4. 解决方案矩阵

| 问题根因 | 解决方案 | 实施难度 | 预期效果 | 优先级 |
|----------|----------|----------|----------|--------|
| **Bundle过大** | 代码分割 + 懒加载 | 中等 | 减少50%初始加载 | 高 |
| **依赖冗余** | Tree shaking + 依赖优化 | 低 | 减少20%体积 | 高 |
| **图片未优化** | WebP格式 + 压缩 | 低 | 减少60%图片大小 | 中 |
| **CDN配置** | 缓存策略优化 | 低 | 提升30%加载速度 | 高 |
| **预加载缺失** | 关键资源预加载 | 低 | 提升20%感知性能 | 中 |

##### 5. 实施计划

**第一阶段（立即执行）：**
```javascript
// 1. 启用代码分割
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

// 2. 优化图片加载
<img
  src="image.webp"
  loading="lazy"
  decoding="async"
/>

// 3. 预加载关键资源
<link rel="preload" href="/critical.css" as="style">
<link rel="preload" href="/critical.js" as="script">
```

**第二阶段（1周内）：**
- 配置CDN缓存策略
- 实施Service Worker缓存
- 优化字体加载策略

**第三阶段（2周内）：**
- 实施SSR/SSG
- 配置HTTP/2推送
- 建立性能监控

##### 6. 预防措施

**性能预算：**
```json
{
  "budgets": [
    {
      "type": "initial",
      "maximumWarning": "500kb",
      "maximumError": "1mb"
    },
    {
      "type": "anyComponentStyle",
      "maximumWarning": "2kb",
      "maximumError": "4kb"
    }
  ]
}
```

**持续监控：**
- 集成Lighthouse CI
- 设置性能告警
- 定期性能审计
- 用户体验监控

#### 案例2：内存泄漏调试
```
问题报告：
单页应用运行一段时间后变得卡顿，内存使用持续增长，
最终导致页面崩溃。主要在用户频繁切换页面时出现。
```

**Debug模式内存分析：**

##### 内存泄漏诊断流程

**1. 内存使用模式分析**
```javascript
// 监控内存使用
function monitorMemory() {
  if (performance.memory) {
    console.log({
      used: Math.round(performance.memory.usedJSHeapSize / 1048576) + 'MB',
      total: Math.round(performance.memory.totalJSHeapSize / 1048576) + 'MB',
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) + 'MB'
    });
  }
}

setInterval(monitorMemory, 5000);
```

**2. 常见泄漏源排查**
- 未清理的事件监听器
- 定时器未清除
- 闭包引用未释放
- DOM节点引用保持
- 全局变量累积

**3. 修复方案**
```javascript
// React组件内存泄漏修复
useEffect(() => {
  const handleResize = () => {
    // 处理逻辑
  };

  window.addEventListener('resize', handleResize);

  // 清理函数
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);
```

![Debug模式界面](./images/roocode-debug-mode.png)

## 🔄 模式切换与高级配置

### 智能模式切换

#### 快速切换方法
1. **界面切换**
   - 点击聊天输入框左下角的模式选择器
   - 从下拉菜单中选择目标模式
   - 模式立即生效，保持对话上下文

2. **快捷键切换**（可配置）
   ```json
   {
     "roocode.shortcuts": {
       "switchToCode": "Ctrl+1",
       "switchToArchitect": "Ctrl+2",
       "switchToAsk": "Ctrl+3",
       "switchToDebug": "Ctrl+4"
     }
   }
   ```

3. **智能模式建议**
   - RooCode会根据对话内容智能建议切换模式
   - 例如：讨论架构时建议切换到Architect模式

### 模式独立配置

#### 针对项目的模式配置
```json
{
  "roocode.modes": {
    "code": {
      "provider": "openai-compatible",
      "baseUrl": "http://132.147.223.249:4000",
      "model": "qwen3-235b-a22b",
      "temperature": 0.1,
      "maxTokens": 4000,
      "systemPrompt": "你是一个专业的代码实现专家..."
    },
    "architect": {
      "provider": "openai-compatible",
      "baseUrl": "http://132.147.223.249:4000",
      "model": "qwen3-235b-a22b",
      "temperature": 0.3,
      "maxTokens": 6000,
      "systemPrompt": "你是一个资深的系统架构师..."
    },
    "ask": {
      "provider": "openai-compatible",
      "baseUrl": "http://132.147.223.249:4000",
      "model": "qwen3-235b-a22b",
      "temperature": 0.2,
      "maxTokens": 3000,
      "systemPrompt": "你是一个知识渊博的技术顾问..."
    },
    "debug": {
      "provider": "openai-compatible",
      "baseUrl": "http://132.147.223.249:4000",
      "model": "qwen3-235b-a22b",
      "temperature": 0.1,
      "maxTokens": 5000,
      "systemPrompt": "你是一个经验丰富的调试专家..."
    }
  }
}
```

#### 模式特定优化参数

| 模式 | Temperature | Max Tokens | 优化目标 |
|------|-------------|------------|----------|
| **Code** | 0.1 | 4000 | 精确性、一致性 |
| **Architect** | 0.3 | 6000 | 创造性、全面性 |
| **Ask** | 0.2 | 3000 | 清晰性、教育性 |
| **Debug** | 0.1 | 5000 | 逻辑性、系统性 |

### 自定义专业模式

#### 创建领域专家模式

**前端性能专家模式：**
```json
{
  "roocode.customModes": {
    "performance": {
      "baseMode": "debug",
      "name": "前端性能专家",
      "icon": "⚡",
      "description": "专注于前端性能优化和调试",
      "systemPrompt": `你是一个前端性能优化专家，具备以下专业能力：

      1. 性能分析：Core Web Vitals、Lighthouse、性能监控
      2. 优化策略：代码分割、懒加载、缓存策略、CDN优化
      3. 调试工具：Chrome DevTools、性能分析器、内存分析
      4. 最佳实践：性能预算、持续监控、性能文化建设

      请始终从性能角度分析问题，提供具体的优化建议和实施方案。`,
      "model": "qwen3-235b-a22b",
      "temperature": 0.1,
      "maxTokens": 5000
    }
  }
}
```

**DevOps专家模式：**
```json
{
  "devops": {
    "baseMode": "architect",
    "name": "DevOps专家",
    "icon": "🚀",
    "description": "专注于CI/CD、部署和运维",
    "systemPrompt": `你是一个DevOps专家，专长包括：

    1. CI/CD流水线设计和优化
    2. 容器化和Kubernetes部署
    3. 监控、日志和告警系统
    4. 基础设施即代码(IaC)
    5. 安全和合规性管理

    请从运维和部署角度提供专业建议。`,
    "model": "qwen3-235b-a22b",
    "temperature": 0.2
  }
}
```

### 模式记忆与上下文管理

#### 智能上下文保持
```json
{
  "roocode.contextManagement": {
    "enableCrossMode": true,          // 跨模式上下文共享
    "maxContextLength": 10000,        // 最大上下文长度
    "contextSummary": true,           // 自动生成上下文摘要
    "projectContext": {               // 项目级上下文
      "enabled": true,
      "includeFileStructure": true,
      "includeGitInfo": true,
      "includePackageInfo": true
    }
  }
}
```

#### 模式切换策略
- **保持上下文**：切换模式时保留对话历史
- **角色转换**：平滑过渡到新模式的专业角色
- **智能摘要**：长对话自动生成摘要传递给新模式

## 📹 多模式演示视频

<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/roocode-multi-mode-demo.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>

## 🏆 最佳实践与工作流

### 标准开发工作流

#### 完整项目开发流程
```mermaid
graph TD
    A[项目启动] --> B[Ask模式：技术调研]
    B --> C[Architect模式：架构设计]
    C --> D[Code模式：功能实现]
    D --> E[Debug模式：问题解决]
    E --> F[Ask模式：优化建议]
    F --> G[项目完成]

    D --> H[测试发现问题]
    H --> E

    C --> I[设计需要调整]
    I --> C
```

#### 各阶段模式应用

**1. 项目规划阶段**
```
Ask模式：
- 了解技术趋势和最佳实践
- 学习新技术和框架
- 获取行业经验和建议

Architect模式：
- 制定技术架构方案
- 进行技术选型决策
- 设计系统集成策略
```

**2. 开发实施阶段**
```
Code模式：
- 实现具体功能模块
- 编写测试代码
- 进行代码重构

Debug模式：
- 解决开发中的问题
- 性能优化和调试
- 集成测试问题排查
```

### 团队协作最佳实践

#### 模式使用规范
```json
{
  "teamStandards": {
    "codeMode": {
      "codeReview": "所有AI生成的代码必须经过人工审查",
      "testing": "新功能必须包含单元测试",
      "documentation": "复杂逻辑需要添加注释"
    },
    "architectMode": {
      "documentation": "架构决策需要记录在ADR中",
      "review": "重大架构变更需要团队评审",
      "validation": "架构方案需要POC验证"
    },
    "askMode": {
      "knowledge": "学习成果需要团队分享",
      "documentation": "重要知识点记录在团队wiki",
      "mentoring": "新人培训优先使用Ask模式"
    },
    "debugMode": {
      "documentation": "问题解决过程需要记录",
      "prevention": "建立问题预防机制",
      "monitoring": "关键问题需要监控告警"
    }
  }
}
```

#### 效率提升技巧

**1. 模式模板化**
```json
{
  "roocode.templates": {
    "newFeature": {
      "sequence": ["ask", "architect", "code", "debug"],
      "prompts": {
        "ask": "请介绍{feature}的最佳实践和注意事项",
        "architect": "请设计{feature}的技术架构",
        "code": "请实现{feature}的核心功能",
        "debug": "请检查{feature}的潜在问题"
      }
    }
  }
}
```

**2. 快捷指令**
```
/code 创建登录组件
/arch 设计微服务架构
/ask React Hooks最佳实践
/debug 内存泄漏问题
```

**3. 项目上下文配置**
```json
{
  "roocode.projectContext": {
    "name": "电商平台",
    "tech": ["React", "Node.js", "MongoDB"],
    "patterns": ["微服务", "DDD", "CQRS"],
    "constraints": ["性能优先", "安全合规", "可扩展"]
  }
}
```

### 质量保证策略

#### 输出质量控制
- **Code模式**：代码审查、测试覆盖、性能检查
- **Architect模式**：架构评审、技术验证、风险评估
- **Ask模式**：知识验证、实践检验、持续更新
- **Debug模式**：解决验证、预防措施、监控建立

#### 持续改进机制
1. **使用反馈收集**：记录各模式的使用效果
2. **模式优化调整**：根据反馈优化配置参数
3. **团队经验分享**：定期分享使用技巧和最佳实践
4. **工具链集成**：与开发工具链深度集成

## 📚 相关资源

- [RooCode安装配置指南](./install-guide.md)
- [API密钥管理指南](../faq/common-faq.md#api密钥相关)
- [平台工具下载中心](../../)

---

*最后更新时间：2025年1月 | 版本：v2.0*