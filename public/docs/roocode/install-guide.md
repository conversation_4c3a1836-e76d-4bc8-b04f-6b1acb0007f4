# RooCode插件安装配置指南

> **RooCode** 是一款创新的多模式AI编程助手，提供Code、Architect、Ask、Debug四种专业模式，适应不同开发场景。支持VS Code、Cursor等主流编辑器，让AI辅助开发更加智能和高效。

![安装过程示例](./images/roocode-settings-1.png)
![安装过程示例](./images/roocode-settings-2.png)


## 📋 准备工作

### 系统要求

| 项目 | 最低要求 | 推荐配置 | 说明 |
|------|----------|----------|------|
| **编辑器版本** | VS Code 1.84.0+ | 最新稳定版 | 支持VS Code兼容编辑器 |
| **操作系统** | Windows 10+, macOS 10.15+, Linux | 最新版本 | 64位系统 |
| **内存** | 4GB RAM | 8GB+ RAM | 确保流畅运行 |
| **存储空间** | 100MB可用空间 | 500MB+ | 包含缓存和日志 |
| **网络环境** | 内网连接 | 稳定高速连接 | 访问AI服务 |

### 支持的编辑器

RooCode支持以下编辑器：

| 编辑器 | 支持程度 | 特殊说明 |
|--------|----------|----------|
| **VS Code** | ✅ 完全支持 | 官方推荐，功能最完整 |
| **Cursor** | ✅ 完全支持 | AI编程专用编辑器 |
| **Windsurf** | ✅ 完全支持 | 新兴AI编辑器 |
| **VS Code Insiders** | ✅ 完全支持 | 内测版本 |
| **Zed** | ⚠️ 部分支持 | 基础功能可用 |
| **其他VS Code兼容** | ⚠️ 视情况而定 | 需要测试验证 |

### 前置条件检查

在开始安装前，请确认：

- [ ] 编辑器版本满足要求
- [ ] 已获得平台账户和API密钥
- [ ] 网络可以访问 `http://132.147.223.249:4000`
- [ ] 具备管理员权限（如需要）
- [ ] 已备份重要的编辑器配置

> ⚠️ **重要提示**：RooCode需要网络连接才能正常工作，请确保网络环境稳定。

![系统要求检查](./images/roocode-system-requirements.png)

## 📦 获取安装包

### 步骤1：访问下载中心

1. **登录平台**
   - 打开浏览器，访问平台主页
   - 使用您的账户凭据登录

2. **导航到下载页面**
   - 点击导航栏中的 **"工具下载中心"**
   - 在页面中找到 **"RooCode插件"** 部分

### 步骤2：下载安装包

1. **获取安装文件**
   - 点击 **"安装指南"** 按钮
   - 在页面顶部找到下载链接
   - 下载 `.vsix` 格式的安装包

2. **文件管理**
   - 将安装包保存到易于访问的位置
   - 建议创建专门的插件文件夹
   - 记录文件路径以便后续使用

> 💡 **提示**：建议下载最新版本的安装包，以获得最佳功能和安全性。

## 🚀 安装步骤详解

### 方法一：VS Code安装（推荐）

#### 步骤1：准备安装
1. **启动VS Code**
   - 打开VS Code编辑器
   - 确保没有重要工作未保存

2. **检查扩展管理器**
   - 按 `Ctrl+Shift+X`（Mac: `Cmd+Shift+X`）
   - 确认扩展管理器正常工作

#### 步骤2：执行安装
1. **打开命令面板**
   - Windows/Linux: `Ctrl+Shift+P`
   - macOS: `Cmd+Shift+P`

2. **执行安装命令**
   - 输入：`Extensions: Install from VSIX`
   - 选择对应的命令选项

3. **选择安装包**
   - 浏览到下载的 `.vsix` 文件位置
   - 选择RooCode插件安装包
   - 点击 **"安装"** 按钮

4. **等待安装完成**
   - VS Code会显示安装进度
   - 安装成功后显示确认消息

#### 步骤3：验证安装
1. **重启编辑器**
   - 完全关闭VS Code
   - 重新启动以确保插件加载

2. **检查安装结果**
   - 在左侧活动栏查找RooCode图标（袋鼠图标）
   - 在扩展管理器中确认插件已启用

### 方法二：Cursor安装

#### 安装流程
1. **启动Cursor编辑器**
   - 打开Cursor应用程序
   - 确保编辑器正常运行

2. **安装插件**
   - 按 `Ctrl+Shift+P`（Mac: `Cmd+Shift+P`）
   - 输入：`Extensions: Install from VSIX`
   - 选择RooCode插件 `.vsix` 文件
   - 等待安装完成

3. **重启验证**
   - 重启Cursor编辑器
   - 检查左侧活动栏的RooCode图标

### 方法三：其他编辑器安装

#### Windsurf编辑器
```bash
# 通过命令行安装（如果支持）
windsurf --install-extension path/to/roocode.vsix
```

#### 通用安装方法
1. 打开编辑器的扩展管理器
2. 查找"从VSIX安装"选项
3. 选择RooCode安装包
4. 按照提示完成安装

### 安装验证清单

安装完成后，请验证以下项目：

- [ ] 左侧活动栏显示RooCode图标（袋鼠）
- [ ] 点击图标能打开RooCode面板
- [ ] 扩展管理器中显示RooCode已启用
- [ ] 没有错误提示或警告信息

### 故障排除

#### 安装失败
**可能原因：**
- 编辑器版本过低
- 安装包损坏
- 权限不足

**解决方案：**
1. 检查编辑器版本
2. 重新下载安装包
3. 以管理员权限运行编辑器
4. 清理扩展缓存后重试

#### 图标不显示
**解决步骤：**
1. 重启编辑器
2. 检查扩展是否启用
3. 查看输出面板的错误信息
4. 重新安装插件



## ⚙️ API配置详解

### 配置概览

RooCode需要连接到AI服务才能提供智能功能。本项目使用内网部署的AI服务，配置简单高效。

| 配置项 | 值 | 说明 |
|--------|----|----|
| **API供应商** | OpenAI Compatible | 兼容OpenAI API格式 |
| **API地址** | `http://132.147.223.249:4000` | 内网AI服务地址 |
| **模型名称** | `qwen3-235b-a22b` | 指定的AI模型 |
| **API密钥** | 从平台获取 | 身份验证凭据 |

### 步骤1：获取API密钥

#### 通过平台获取
1. **访问密钥管理页面**
   - 登录平台主页
   - 导航至 **"API密钥管理"**

2. **生成新密钥**
   - 点击 **"生成新密钥"** 按钮
   - 设置描述名称：`RooCode插件使用`
   - 复制生成的API密钥

3. **安全保存**
   - 将密钥保存到安全位置
   - 避免在公共场所暴露密钥

> 🔐 **安全提醒**：API密钥是重要的身份凭据，请妥善保管，定期更换。

### 步骤2：配置RooCode

#### 方法一：图形界面配置（推荐新手）

1. **打开配置面板**
   - 点击RooCode面板右上角的齿轮图标⚙️
   - 选择 **"Settings"** 或 **"Providers"**

2. **添加API供应商**
   - 点击 **"Add Provider"** 按钮
   - 选择 **"OpenAI Compatible"** 类型

3. **填写配置信息**

   **基本信息：**
   - **Provider Name**: `内网AI平台`
   - **API Type**: `OpenAI Compatible`

   **连接设置：**
   - **Base URL**: `http://132.147.223.249:4000`
   - **Model**: `qwen3-235b-a22b`
   - **API Key**: 粘贴您的API密钥

   **高级设置：**
   - **Max Tokens**: `4000`
   - **Temperature**: `0.7`
   - **Timeout**: `30000`

4. **保存并测试**
   - 点击 **"Save"** 保存配置
   - 点击 **"Test Connection"** 验证连接

#### 方法二：配置文件编辑（推荐高级用户）

1. **打开设置文件**
   - 按 `Ctrl+,`（Mac: `Cmd+,`）打开设置
   - 点击右上角的 **"Open Settings (JSON)"** 图标

2. **添加配置**
   ```json
   {
     "roocode.providers": [
       {
         "id": "ynnx-ai-platform",
         "name": "内网AI平台",
         "type": "openai-compatible",
         "baseUrl": "http://132.147.223.249:4000",
         "model": "qwen3-235b-a22b",
         "apiKey": "your-actual-api-key-here",
         "maxTokens": 4000,
         "temperature": 0.7,
         "timeout": 30000
       }
     ],
     "roocode.defaultProvider": "ynnx-ai-platform"
   }
   ```

> 📝 **重要**：请将 `your-actual-api-key-here` 替换为您的真实API密钥。

### 步骤3：验证配置

#### 连接测试
1. **自动测试**
   - 保存配置后，RooCode会自动测试连接
   - 查看状态指示器的颜色变化

2. **手动测试**
   - 在RooCode面板中发送测试消息
   - 例如：`你好，请介绍一下你的功能`

3. **验证结果**
   - ✅ **成功**：收到AI回复，配置正确
   - ❌ **失败**：检查配置参数和网络连接

#### 常见配置问题

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| **连接超时** | 请求无响应 | 检查网络连接和API地址 |
| **认证失败** | 401错误 | 验证API密钥是否正确 |
| **模型不存在** | 404错误 | 确认模型名称：`qwen3-235b-a22b` |
| **配额超限** | 429错误 | 联系管理员或等待配额重置 |

### 高级配置选项

#### 对话设置
```json
{
  "roocode.conversation": {
    "maxMessages": 50,              // 最大对话历史
    "maxTokensPerMessage": 1000,    // 单条消息最大长度
    "enableContext": true,          // 启用上下文理解
    "autoSave": true                // 自动保存对话
  }
}
```

#### 代码生成设置
```json
{
  "roocode.codeGeneration": {
    "autoFormat": true,             // 自动格式化代码
    "includeComments": true,        // 包含注释
    "followProjectStyle": true,     // 遵循项目风格
    "generateTests": false,         // 自动生成测试
    "optimizeImports": true         // 优化导入语句
  }
}
```

#### 界面定制
```json
{
  "roocode.ui": {
    "theme": "dark",                // 主题：dark/light/auto
    "fontSize": 14,                 // 字体大小
    "showLineNumbers": true,        // 显示行号
    "enableSyntaxHighlight": true,  // 语法高亮
    "compactMode": false            // 紧凑模式
  }
}
```

#### 性能优化
```json
{
  "roocode.performance": {
    "enableCache": true,            // 启用缓存
    "cacheSize": "100MB",          // 缓存大小
    "preloadModels": false,        // 预加载模型
    "batchRequests": true          // 批量请求
  }
}
```

## 💡 首次使用指南

### 启动RooCode

#### 步骤1：打开项目
1. **选择项目**
   - 在编辑器中打开您的项目文件夹
   - 确保项目结构清晰，便于AI理解

2. **启动RooCode**
   - 点击左侧活动栏的RooCode图标（袋鼠图标🦘）
   - RooCode面板将在侧边栏中打开

#### 步骤2：界面介绍

RooCode界面主要包含以下区域：

| 区域 | 功能 | 说明 |
|------|------|------|
| **模式选择器** | 切换工作模式 | 位于输入框上方 |
| **对话区域** | 显示对话历史 | 主要交互区域 |
| **输入框** | 输入指令和问题 | 支持多行输入 |
| **工具栏** | 快捷操作按钮 | 清除、导出等功能 |
| **状态栏** | 显示连接状态 | 底部状态信息 |

### 四种工作模式详解

RooCode的核心特色是四种专业工作模式，每种模式针对不同的开发场景优化：

#### 🔨 Code模式 - 通用编程助手
**适用场景：**
- 日常代码编写和修改
- 函数和类的实现
- 代码重构和优化
- 单元测试编写

**使用示例：**
```
请帮我创建一个React用户登录组件，包含：
- 用户名和密码输入框
- 表单验证
- 登录状态管理
- 错误处理
```

#### 🏗️ Architect模式 - 系统架构师
**适用场景：**
- 系统架构设计
- 技术选型建议
- 数据库设计
- 微服务规划

**使用示例：**
```
我需要设计一个电商系统的后端架构，要求：
- 支持高并发
- 微服务架构
- 数据一致性
- 可扩展性
请提供详细的架构方案和技术选型建议。
```

#### ❓ Ask模式 - 技术顾问
**适用场景：**
- 技术概念解释
- 最佳实践咨询
- 工具使用指导
- 学习路径规划

**使用示例：**
```
什么是React Hooks？它相比Class组件有什么优势？
请详细解释useState和useEffect的使用方法。
```

#### 🐛 Debug模式 - 调试专家
**适用场景：**
- 错误诊断和修复
- 性能问题分析
- 代码审查
- 系统故障排除

**使用示例：**
```
我的React应用出现内存泄漏，页面切换后内存持续增长。
请帮我分析可能的原因并提供解决方案。

相关代码：
[粘贴相关代码片段]
```

### 基本使用流程

#### 1. 选择合适的模式
根据您的需求选择最合适的工作模式：
- 编程任务 → Code模式
- 架构设计 → Architect模式
- 技术咨询 → Ask模式
- 问题调试 → Debug模式

#### 2. 描述需求
在输入框中详细描述您的需求：
- 使用清晰、具体的语言
- 提供必要的上下文信息
- 包含相关的代码片段（如适用）

#### 3. 审查建议
RooCode会提供详细的回复和建议：
- 仔细阅读AI的分析和建议
- 检查提供的代码示例
- 理解解决方案的原理

#### 4. 应用和迭代
根据建议进行操作：
- 应用有用的建议
- 测试实现效果
- 根据结果进行进一步优化

### 高效使用技巧

#### 上下文提供
- **项目信息**：说明项目类型、技术栈、规模
- **具体需求**：明确功能要求和约束条件
- **代码上下文**：提供相关的代码片段

#### 分步骤协作
```
第一步：请分析我的项目结构并提出改进建议
第二步：基于分析结果，帮我重构用户模块
第三步：为重构后的代码添加单元测试
```

#### 模式切换
根据对话进展切换模式：
```
[Code模式] 请实现用户注册功能
[Debug模式] 注册功能出现验证错误，请帮我调试
[Ask模式] 用户注册的最佳实践是什么？
```

![RooCode界面示例](./images/roocode-interface-overview.png)

## 📹 安装演示视频

<video controls width="100%" style="max-width: 800px;">
  <source src="./videos/roocode-installation-demo.mp4" type="video/mp4">
  您的浏览器不支持视频播放。
</video>

## 🔧 故障排除与常见问题

### 安装相关问题

#### Q: 插件安装后看不到袋鼠图标？
**症状：** 安装完成但左侧活动栏没有RooCode图标

**解决方案：**
1. **重启编辑器**
   - 完全关闭编辑器
   - 重新启动并等待插件加载

2. **检查扩展状态**
   - 按 `Ctrl+Shift+X`（Mac: `Cmd+Shift+X`）
   - 搜索 "RooCode"
   - 确保插件已启用（显示为蓝色）

3. **查看错误日志**
   - `View` → `Output`
   - 选择 "RooCode" 通道
   - 查看是否有错误信息

4. **重新安装**
   - 卸载现有插件
   - 重新下载并安装最新版本

#### Q: 安装过程中出现权限错误？
**解决方案：**
- Windows: 以管理员身份运行编辑器
- macOS: 使用 `sudo` 权限或检查文件权限
- Linux: 确保用户有写入权限

### 配置相关问题

#### Q: API连接测试失败？
**系统性排查：**

1. **网络连通性测试**
   ```bash
   # 测试服务器连通性
   ping 132.147.223.249

   # 测试端口可达性
   telnet 132.147.************

   # HTTP请求测试
   curl -I http://132.147.223.249:4000
   ```

2. **配置参数检查**
   - API地址：`http://132.147.223.249:4000`
   - 模型名称：`qwen3-235b-a22b`
   - API密钥格式和有效性

3. **网络环境检查**
   - 防火墙设置
   - 代理配置
   - VPN连接状态

#### Q: 模型响应错误或异常？
**可能原因和解决方案：**

| 错误类型 | 可能原因 | 解决方案 |
|----------|----------|----------|
| 401 Unauthorized | API密钥无效 | 重新生成密钥 |
| 404 Not Found | 模型名称错误 | 确认使用 `qwen3-235b-a22b` |
| 429 Too Many Requests | 请求频率过高 | 降低请求频率或联系管理员 |
| 500 Internal Server Error | 服务器内部错误 | 联系技术支持 |

### 使用相关问题

#### Q: 如何在多个编辑器间同步配置？
**方法一：配置导入导出**
1. **导出配置**
   - 在已配置的编辑器中打开RooCode设置
   - 点击 `Settings` → `Export Configuration`
   - 保存配置文件到本地

2. **导入配置**
   - 在新编辑器中打开RooCode设置
   - 点击 `Settings` → `Import Configuration`
   - 选择之前导出的配置文件

**方法二：手动同步**
```json
// 复制以下配置到新编辑器的settings.json
{
  "roocode.providers": [...],
  "roocode.defaultProvider": "ynnx-ai-platform",
  "roocode.conversation": {...},
  "roocode.codeGeneration": {...}
}
```

#### Q: 不同模式之间有什么区别？
**详细对比：**

| 特性 | Code模式 | Architect模式 | Ask模式 | Debug模式 |
|------|----------|---------------|---------|-----------|
| **主要用途** | 代码编写 | 架构设计 | 技术咨询 | 问题调试 |
| **输出类型** | 代码片段 | 设计方案 | 知识解答 | 解决方案 |
| **上下文深度** | 中等 | 深度 | 浅层 | 深度 |
| **交互方式** | 任务导向 | 讨论式 | 问答式 | 分析式 |

### 性能优化

#### 内存使用优化
```json
{
  "roocode.performance": {
    "maxCacheSize": "100MB",        // 限制缓存大小
    "enableLazyLoading": true,      // 启用懒加载
    "compressHistory": true,        // 压缩历史记录
    "maxHistoryItems": 100,         // 限制历史条目
    "autoCleanup": true             // 自动清理临时文件
  }
}
```

#### 网络性能优化
```json
{
  "roocode.network": {
    "timeout": 30000,               // 请求超时时间
    "retryAttempts": 3,             // 重试次数
    "enableCompression": true,      // 启用压缩
    "batchRequests": false,         // 禁用批量请求
    "keepAlive": true               // 保持连接
  }
}
```

#### 响应速度优化
```json
{
  "roocode.response": {
    "streamMode": true,             // 流式响应
    "maxTokens": 2000,              // 限制响应长度
    "temperature": 0.5,             // 降低创造性
    "enableCache": true             // 启用响应缓存
  }
}
```

## 📊 使用监控与统计

### 查看使用统计
1. **访问统计面板**
   - 点击RooCode面板右上角的统计图标📊
   - 或使用命令：`RooCode: Show Statistics`

2. **关键指标**
   - API调用次数和成功率
   - 平均响应时间
   - 配额使用情况
   - 各模式使用频率

### 性能监控
```json
{
  "roocode.monitoring": {
    "enableMetrics": true,          // 启用指标收集
    "reportInterval": 300,          // 报告间隔（秒）
    "logLevel": "info",             // 日志级别
    "metricsFile": "./logs/roocode-metrics.json"
  }
}
```

### 使用建议
- **定期检查**：每周查看使用统计
- **优化配置**：根据使用模式调整设置
- **监控配额**：避免超出API使用限制
- **性能调优**：根据响应时间优化配置

## 📚 相关资源

- [RooCode多模式使用指南](./multi-mode.md)
- [API密钥管理指南](../faq/common-faq.md#api密钥相关)
- [平台工具下载中心](../../)

---

*最后更新时间：2025年1月 | 版本：v2.0*